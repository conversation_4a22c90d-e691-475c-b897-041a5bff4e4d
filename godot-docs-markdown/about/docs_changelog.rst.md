allow_comments

:   False

# Documentation changelog {#doc_docs_changelog}

The documentation is continually being improved. New releases include
new pages, fixes and updates to existing pages, and many updates to the
`class reference <doc_class_reference>`{.interpreted-text role="ref"}.
Below is a list of new pages added since version 3.0.

::: note
::: title
Note
:::

This document only contains new pages so not all changes are reflected,
many pages have been substantially updated but are not reflected in this
document.
:::

## New pages since version 4.3

### 2D

-   `doc_introduction_to_2d`{.interpreted-text role="ref"}

### 3D

-   `doc_spring_arm`{.interpreted-text role="ref"}

### Debug

-   `doc_output_panel`{.interpreted-text role="ref"}

### Editor

-   `doc_using_the_xr_editor`{.interpreted-text role="ref"}

### Performance

-   `doc_pipeline_compilations`{.interpreted-text role="ref"}

### Physics

-   `doc_physics_interpolation`{.interpreted-text role="ref"}
-   `doc_physics_interpolation_quick_start_guide`{.interpreted-text
    role="ref"}
-   `doc_physics_interpolation_introduction`{.interpreted-text
    role="ref"}
-   `doc_using_physics_interpolation`{.interpreted-text role="ref"}
-   `doc_advanced_physics_interpolation`{.interpreted-text role="ref"}
-   `doc_2d_and_3d_physics_interpolation`{.interpreted-text role="ref"}

### Rendering

-   `doc_renderers`{.interpreted-text role="ref"}

### Shaders

-   `doc_shader_functions`{.interpreted-text role="ref"}

## New pages since version 4.2

### About

-   `doc_system_requirements`{.interpreted-text role="ref"}

### 2D

-   `doc_2d_parallax`{.interpreted-text role="ref"}

### Contributing

-   `doc_handling_compatibility_breakages`{.interpreted-text role="ref"}
-   `doc_ways_to_contribute`{.interpreted-text role="ref"}

### GDExtension

-   `doc_gdextension_file`{.interpreted-text role="ref"}
-   `doc_gdextension_docs_system`{.interpreted-text role="ref"}

### Migrating

-   `doc_upgrading_to_godot_4.3`{.interpreted-text role="ref"}

### Rendering

-   `doc_compositor`{.interpreted-text role="ref"}

### XR

-   `doc_a_better_xr_start_script`{.interpreted-text role="ref"}
-   `doc_openxr_passthrough`{.interpreted-text role="ref"}
-   `doc_xr_next_steps`{.interpreted-text role="ref"}
-   `doc_openxr_settings`{.interpreted-text role="ref"}
-   `doc_openxr_composition_layers`{.interpreted-text role="ref"}
-   `doc_openxr_body_tracking`{.interpreted-text role="ref"}

## New pages since version 4.1

### C#

-   `doc_c_sharp_diagnostics`{.interpreted-text role="ref"}

### Development

-   `doc_2d_coordinate_systems`{.interpreted-text role="ref"}

### Migrating

-   `doc_upgrading_to_godot_4.2`{.interpreted-text role="ref"}

### I/O

-   `doc_runtime_loading_and_saving`{.interpreted-text role="ref"}

### Platform-specific

-   `doc_android_library`{.interpreted-text role="ref"}

## New pages since version 4.0

### Development

-   `doc_internal_rendering_architecture`{.interpreted-text role="ref"}
-   `doc_using_sanitizers`{.interpreted-text role="ref"}

### Migrating

-   `doc_upgrading_to_godot_4.1`{.interpreted-text role="ref"}

### Physics

-   `doc_troubleshooting_physics_issues`{.interpreted-text role="ref"}

## New pages since version 3.6

### 2D

-   `doc_2d_antialiasing`{.interpreted-text role="ref"}

### 3D

-   `doc_3d_antialiasing`{.interpreted-text role="ref"}
-   `doc_faking_global_illumination`{.interpreted-text role="ref"}
-   `doc_introduction_to_global_illumination`{.interpreted-text
    role="ref"}
-   `doc_mesh_lod`{.interpreted-text role="ref"}
-   `doc_occlusion_culling`{.interpreted-text role="ref"}
-   `doc_using_sdfgi`{.interpreted-text role="ref"}
-   `doc_using_decals`{.interpreted-text role="ref"}
-   `doc_visibility_ranges`{.interpreted-text role="ref"}
-   `doc_volumetric_fog`{.interpreted-text role="ref"}
-   `doc_variable_rate_shading`{.interpreted-text role="ref"}
-   `doc_physical_light_and_camera_units`{.interpreted-text role="ref"}

### Animation

-   `doc_creating_movies`{.interpreted-text role="ref"}

### Assets pipeline

-   `doc_retargeting_3d_skeletons`{.interpreted-text role="ref"}

### Development

-   `doc_custom_platform_ports`{.interpreted-text role="ref"}

### Migrating

-   `doc_upgrading_to_godot_4`{.interpreted-text role="ref"}

### Physics

-   `doc_large_world_coordinates`{.interpreted-text role="ref"}

### Scripting

-   `doc_custom_performance_monitors`{.interpreted-text role="ref"}
-   `doc_c_sharp_collections`{.interpreted-text role="ref"}
-   `doc_c_sharp_global_classes`{.interpreted-text role="ref"}
-   `doc_c_sharp_variant`{.interpreted-text role="ref"}

### Shaders

-   `doc_compute_shaders`{.interpreted-text role="ref"}

### Workflow

-   `doc_pr_review_guidelines`{.interpreted-text role="ref"}

### XR

-   `doc_introducing_xr_tools`{.interpreted-text role="ref"}
-   `doc_xr_action_map`{.interpreted-text role="ref"}
-   `doc_deploying_to_android`{.interpreted-text role="ref"}

## New pages since version 3.5

None.

## New pages since version 3.4

### 3D

-   `doc_3d_text`{.interpreted-text role="ref"}

### Animation

-   `doc_playing_videos`{.interpreted-text role="ref"}

### Editor

-   `doc_managing_editor_features`{.interpreted-text role="ref"}

## New pages since version 3.3

### C++

-   `doc_cpp_usage_guidelines`{.interpreted-text role="ref"}

### GDScript

-   `doc_gdscript_documentation_comments`{.interpreted-text role="ref"}

## New pages since version 3.2

### 3D

-   `doc_3d_rendering_limitations`{.interpreted-text role="ref"}

### About

-   `doc_troubleshooting`{.interpreted-text role="ref"}
-   `doc_list_of_features`{.interpreted-text role="ref"}
-   `doc_release_policy`{.interpreted-text role="ref"}

### Best practices

-   `doc_version_control_systems`{.interpreted-text role="ref"}

### Community

-   `doc_best_practices_for_engine_contributors`{.interpreted-text
    role="ref"}
-   `doc_bisecting_regressions`{.interpreted-text role="ref"}
-   `doc_editor_and_docs_localization`{.interpreted-text role="ref"}

### Development

-   `doc_introduction_to_editor_development`{.interpreted-text
    role="ref"}
-   `doc_editor_style_guide`{.interpreted-text role="ref"}
-   `doc_common_engine_methods_and_macros`{.interpreted-text role="ref"}
-   `doc_vulkan_validation_layers`{.interpreted-text role="ref"}
-   `doc_gdscript_grammar`{.interpreted-text role="ref"}
-   Configuring an IDE:
    `doc_configuring_an_ide_code_blocks`{.interpreted-text role="ref"}

### Editor

-   `doc_default_key_mapping`{.interpreted-text role="ref"}
-   `doc_using_the_web_editor`{.interpreted-text role="ref"}

### Export

-   `doc_exporting_for_dedicated_servers`{.interpreted-text role="ref"}

### Input

-   `doc_controllers_gamepads_joysticks`{.interpreted-text role="ref"}

### Math

-   `doc_random_number_generation`{.interpreted-text role="ref"}

### Platform-specific

-   `doc_plugins_for_ios`{.interpreted-text role="ref"}
-   `doc_ios_plugin`{.interpreted-text role="ref"}
-   `doc_html5_shell_classref`{.interpreted-text role="ref"}

### Physics

-   `doc_collision_shapes_2d`{.interpreted-text role="ref"}
-   `doc_collision_shapes_3d`{.interpreted-text role="ref"}

### Shaders

-   `doc_shaders_style_guide`{.interpreted-text role="ref"}

### Scripting

-   `doc_debugger_panel`{.interpreted-text role="ref"}
-   `doc_creating_script_templates`{.interpreted-text role="ref"}
-   `doc_evaluating_expressions`{.interpreted-text role="ref"}
-   `doc_what_is_gdextension`{.interpreted-text role="ref"}
-   `doc_gdscript_warning_system`{.interpreted-text role="ref"} (split
    from `doc_gdscript_static_typing`{.interpreted-text role="ref"})

### User Interface (UI)

-   `doc_control_node_gallery`{.interpreted-text role="ref"}

## New pages since version 3.1

### Project workflow

-   `doc_android_gradle_build`{.interpreted-text role="ref"}

### 2D

-   `doc_2d_sprite_animation`{.interpreted-text role="ref"}

### Audio

-   `doc_recording_with_microphone`{.interpreted-text role="ref"}
-   `doc_sync_with_audio`{.interpreted-text role="ref"}

### Math

-   `doc_beziers_and_curves`{.interpreted-text role="ref"}
-   `doc_interpolation`{.interpreted-text role="ref"}

### Inputs

-   `doc_input_examples`{.interpreted-text role="ref"}

### Internationalization

-   `doc_localization_using_gettext`{.interpreted-text role="ref"}

### Shading

-   

    Your First Shader Series:

    :   -   `doc_introduction_to_shaders`{.interpreted-text role="ref"}
        -   `doc_your_first_canvasitem_shader`{.interpreted-text
            role="ref"}
        -   `doc_your_first_spatial_shader`{.interpreted-text
            role="ref"}
        -   `doc_your_second_spatial_shader`{.interpreted-text
            role="ref"}

-   `doc_visual_shaders`{.interpreted-text role="ref"}

### Networking

-   `doc_webrtc`{.interpreted-text role="ref"}

### Plugins

-   `doc_android_plugin`{.interpreted-text role="ref"}
-   `doc_inspector_plugins`{.interpreted-text role="ref"}
-   `doc_visual_shader_plugins`{.interpreted-text role="ref"}

### Multi-threading

-   `doc_using_multiple_threads`{.interpreted-text role="ref"}

### Creating content

Procedural geometry series:

:   -   `Procedural geometry <toc-procedural_geometry>`{.interpreted-text
        role="ref"}
    -   `doc_arraymesh`{.interpreted-text role="ref"}
    -   `doc_surfacetool`{.interpreted-text role="ref"}
    -   `doc_meshdatatool`{.interpreted-text role="ref"}
    -   `doc_immediatemesh`{.interpreted-text role="ref"}

### Optimization

-   `doc_using_multimesh`{.interpreted-text role="ref"}
-   `doc_using_servers`{.interpreted-text role="ref"}

### Legal

-   `doc_complying_with_licenses`{.interpreted-text role="ref"}

## New pages since version 3.0

### Step by step

-   `doc_signals`{.interpreted-text role="ref"}
-   Exporting

### Scripting

-   `doc_gdscript_static_typing`{.interpreted-text role="ref"}

### Project workflow

Best Practices:

-   `doc_introduction_best_practices`{.interpreted-text role="ref"}
-   `doc_what_are_godot_classes`{.interpreted-text role="ref"}
-   `doc_scene_organization`{.interpreted-text role="ref"}
-   `doc_scenes_versus_scripts`{.interpreted-text role="ref"}
-   `doc_autoloads_versus_internal_nodes`{.interpreted-text role="ref"}
-   `doc_node_alternatives`{.interpreted-text role="ref"}
-   `doc_godot_interfaces`{.interpreted-text role="ref"}
-   `doc_godot_notifications`{.interpreted-text role="ref"}
-   `doc_data_preferences`{.interpreted-text role="ref"}
-   `doc_logic_preferences`{.interpreted-text role="ref"}

### 2D

-   `doc_2d_lights_and_shadows`{.interpreted-text role="ref"}
-   `doc_2d_meshes`{.interpreted-text role="ref"}

### 3D

-   `doc_csg_tools`{.interpreted-text role="ref"}
-   `doc_animating_thousands_of_fish`{.interpreted-text role="ref"}
-   `doc_controlling_thousands_of_fish`{.interpreted-text role="ref"}

### Physics

-   `doc_ragdoll_system`{.interpreted-text role="ref"}
-   `doc_soft_body`{.interpreted-text role="ref"}

### Animation

-   `doc_2d_skeletons`{.interpreted-text role="ref"}
-   `doc_animation_tree`{.interpreted-text role="ref"}

### GUI

-   `doc_gui_containers`{.interpreted-text role="ref"}

### Viewports

-   `doc_viewport_as_texture`{.interpreted-text role="ref"}
-   `doc_custom_postprocessing`{.interpreted-text role="ref"}

### Shading

-   `doc_converting_glsl_to_godot_shaders`{.interpreted-text role="ref"}
-   `doc_advanced_postprocessing`{.interpreted-text role="ref"}

Shading Reference:

-   `doc_introduction_to_shaders`{.interpreted-text role="ref"}
-   `doc_shading_language`{.interpreted-text role="ref"}
-   `doc_spatial_shader`{.interpreted-text role="ref"}
-   `doc_canvas_item_shader`{.interpreted-text role="ref"}
-   `doc_particle_shader`{.interpreted-text role="ref"}

### Plugins

-   `doc_making_main_screen_plugins`{.interpreted-text role="ref"}
-   `doc_3d_gizmo_plugins`{.interpreted-text role="ref"}

### Platform-specific

-   `doc_customizing_html5_shell`{.interpreted-text role="ref"}

### Multi-threading

-   `doc_thread_safe_apis`{.interpreted-text role="ref"}

### Creating content

-   `doc_making_trees`{.interpreted-text role="ref"}

### Miscellaneous

-   `doc_jitter_stutter`{.interpreted-text role="ref"}
-   `doc_running_code_in_the_editor`{.interpreted-text role="ref"}
-   `doc_change_scenes_manually`{.interpreted-text role="ref"}

### Compiling

-   `doc_optimizing_for_size`{.interpreted-text role="ref"}
-   `doc_compiling_with_script_encryption_key`{.interpreted-text
    role="ref"}

### Engine development

-   `doc_binding_to_external_libraries`{.interpreted-text role="ref"}
