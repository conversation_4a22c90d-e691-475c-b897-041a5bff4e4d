github_url

:   hide

# RenderDataRD {#class_RenderDataRD}

**Inherits:** `RenderData<class_RenderData>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

Render data implementation for the RenderingDevice based renderers.

**Note:** This is an internal rendering server object, do not
instantiate this from script.

::: rst-class
classref-introduction-group
:::

## Description

This object manages all render data for the rendering device based
renderers.

**Note:** This is an internal rendering server object only exposed for
GDExtension plugins.
