github_url

:   hide

# VisualShaderNodeSDFToScreenUV {#class_VisualShaderNodeSDFToScreenUV}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A function to convert an SDF (signed-distance field) to screen UV, to be
used within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Translates to `sdf_to_screen_uv(sdf_pos)` in the shader language.
