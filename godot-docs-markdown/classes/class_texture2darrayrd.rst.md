github_url

:   hide

# Texture2DArrayRD {#class_Texture2DArrayRD}

**Inherits:**
`TextureLayeredRD<class_TextureLayeredRD>`{.interpreted-text role="ref"}
**\<** `TextureLayered<class_TextureLayered>`{.interpreted-text
role="ref"} **\<** `Texture<class_Texture>`{.interpreted-text
role="ref"} **\<** `Resource<class_Resource>`{.interpreted-text
role="ref"} **\<** `RefCounted<class_RefCounted>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

Texture Array for 2D that is bound to a texture created on the
`RenderingDevice<class_RenderingDevice>`{.interpreted-text role="ref"}.

::: rst-class
classref-introduction-group
:::

## Description

This texture array class allows you to use a 2D array texture created
directly on the
`RenderingDevice<class_RenderingDevice>`{.interpreted-text role="ref"}
as a texture for materials, meshes, etc.
