github_url

:   hide

# StyleBoxEmpty {#class_StyleBoxEmpty}

**Inherits:** `StyleBox<class_StyleBox>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

An empty `StyleBox<class_StyleBox>`{.interpreted-text role="ref"} (does
not display anything).

::: rst-class
classref-introduction-group
:::

## Description

An empty `StyleBox<class_StyleBox>`{.interpreted-text role="ref"} that
can be used to display nothing instead of the default style (e.g. it can
\"disable\" `focus` styles).
