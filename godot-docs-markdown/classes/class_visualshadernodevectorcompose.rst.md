github_url

:   hide

# VisualShaderNodeVectorCompose {#class_VisualShaderNodeVectorCompose}

**Inherits:**
`VisualShaderNodeVectorBase<class_VisualShaderNodeVectorBase>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Composes a `Vector2<class_Vector2>`{.interpreted-text role="ref"},
`Vector3<class_Vector3>`{.interpreted-text role="ref"} or 4D vector
(represented as a `Quaternion<class_Quaternion>`{.interpreted-text
role="ref"}) from scalars within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Creates a `vec2`, `vec3` or `vec4` using scalar values that can be
provided from separate inputs.
