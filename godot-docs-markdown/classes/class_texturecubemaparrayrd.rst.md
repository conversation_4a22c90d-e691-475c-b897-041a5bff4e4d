github_url

:   hide

# TextureCubemapArrayRD {#class_TextureCubemapArrayRD}

**Inherits:**
`TextureLayeredRD<class_TextureLayeredRD>`{.interpreted-text role="ref"}
**\<** `TextureLayered<class_TextureLayered>`{.interpreted-text
role="ref"} **\<** `Texture<class_Texture>`{.interpreted-text
role="ref"} **\<** `Resource<class_Resource>`{.interpreted-text
role="ref"} **\<** `RefCounted<class_RefCounted>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

Texture Array for Cubemaps that is bound to a texture created on the
`RenderingDevice<class_RenderingDevice>`{.interpreted-text role="ref"}.

::: rst-class
classref-introduction-group
:::

## Description

This texture class allows you to use a cubemap array texture created
directly on the
`RenderingDevice<class_RenderingDevice>`{.interpreted-text role="ref"}
as a texture for materials, meshes, etc.
