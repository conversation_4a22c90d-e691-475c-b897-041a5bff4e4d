github_url

:   hide

# RenderSceneDataRD {#class_RenderSceneDataRD}

**Inherits:** `RenderSceneData<class_RenderSceneData>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

Render scene data implementation for the RenderingDevice based
renderers.

::: rst-class
classref-introduction-group
:::

## Description

Object holds scene data related to rendering a single frame of a
viewport.

**Note:** This is an internal rendering server object, do not
instantiate this from script.
