github_url

:   hide

# StandardMaterial3D {#class_StandardMaterial3D}

**Inherits:** `BaseMaterial3D<class_BaseMaterial3D>`{.interpreted-text
role="ref"} **\<** `Material<class_Material>`{.interpreted-text
role="ref"} **\<** `Resource<class_Resource>`{.interpreted-text
role="ref"} **\<** `RefCounted<class_RefCounted>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

A PBR (Physically Based Rendering) material to be used on 3D objects.

::: rst-class
classref-introduction-group
:::

## Description

**StandardMaterial3D**\'s properties are inherited from
`BaseMaterial3D<class_BaseMaterial3D>`{.interpreted-text role="ref"}.
**StandardMaterial3D** uses separate textures for ambient occlusion,
roughness and metallic maps. To use a single ORM map for all 3 textures,
use an `ORMMaterial3D<class_ORMMaterial3D>`{.interpreted-text
role="ref"} instead.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `Standard Material 3D and ORM Material 3D <../tutorials/3d/standard_material_3d>`{.interpreted-text
    role="doc"}
