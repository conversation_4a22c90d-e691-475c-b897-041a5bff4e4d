github_url

:   hide

# VScrollBar {#class_VScrollBar}

**Inherits:** `ScrollBar<class_ScrollBar>`{.interpreted-text role="ref"}
**\<** `Range<class_Range>`{.interpreted-text role="ref"} **\<**
`Control<class_Control>`{.interpreted-text role="ref"} **\<**
`CanvasItem<class_CanvasItem>`{.interpreted-text role="ref"} **\<**
`Node<class_Node>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A vertical scrollbar that goes from top (min) to bottom (max).

::: rst-class
classref-introduction-group
:::

## Description

A vertical scrollbar, typically used to navigate through content that
extends beyond the visible height of a control. It is a
`Range<class_Range>`{.interpreted-text role="ref"}-based control and
goes from top (min) to bottom (max). Note that this direction is the
opposite of `VSlider<class_VSlider>`{.interpreted-text role="ref"}\'s.

::: rst-class
classref-reftable-group
:::

## Properties

