github_url

:   hide

# ResourceImporterShaderFile {#class_ResourceImporterShaderFile}

**Inherits:**
`ResourceImporter<class_ResourceImporter>`{.interpreted-text role="ref"}
**\<** `RefCounted<class_RefCounted>`{.interpreted-text role="ref"}
**\<** `Object<class_Object>`{.interpreted-text role="ref"}

Imports native GLSL shaders (not Godot shaders) as a
`RDShaderFile<class_RDShaderFile>`{.interpreted-text role="ref"}.

::: rst-class
classref-introduction-group
:::

## Description

This imports native GLSL shaders as
`RDShaderFile<class_RDShaderFile>`{.interpreted-text role="ref"}
resources, for use with low-level
`RenderingDevice<class_RenderingDevice>`{.interpreted-text role="ref"}
operations. This importer does *not* handle `.gdshader` files.
