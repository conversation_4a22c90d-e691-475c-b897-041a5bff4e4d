github_url

:   hide

# VisualShaderNodeIntConstant {#class_VisualShaderNodeIntConstant}

**Inherits:**
`VisualShaderNodeConstant<class_VisualShaderNodeConstant>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A scalar integer constant to be used within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Translated to `int` in the shader language.

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Property Descriptions

::: {#class_VisualShaderNodeIntConstant_property_constant}
::: rst-class
classref-property
:::
:::

`int<class_int>`{.interpreted-text role="ref"} **constant** = `0`
`🔗<class_VisualShaderNodeIntConstant_property_constant>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_constant**(value: `int<class_int>`{.interpreted-text
    role="ref"})
-   `int<class_int>`{.interpreted-text role="ref"} **get_constant**()

An integer constant which represents a state of this node.
