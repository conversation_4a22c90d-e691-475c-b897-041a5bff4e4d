github_url

:   hide

# VisualShaderNodeDistanceFade {#class_VisualShaderNodeDistanceFade}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A visual shader node representing distance fade effect.

::: rst-class
classref-introduction-group
:::

## Description

The distance fade effect fades out each pixel based on its distance to
another object.
