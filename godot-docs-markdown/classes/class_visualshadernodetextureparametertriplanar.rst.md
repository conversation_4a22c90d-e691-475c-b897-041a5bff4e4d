github_url

:   hide

# VisualShaderNodeTextureParameterTriplanar {#class_VisualShaderNodeTextureParameterTriplanar}

**Inherits:**
`VisualShaderNodeTextureParameter<class_VisualShaderNodeTextureParameter>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNodeParameter<class_VisualShaderNodeParameter>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Performs a uniform texture lookup with triplanar within the visual
shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Performs a lookup operation on the texture provided as a uniform for the
shader, with support for triplanar mapping.
