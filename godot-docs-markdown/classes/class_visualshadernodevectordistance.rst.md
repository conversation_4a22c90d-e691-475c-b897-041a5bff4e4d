github_url

:   hide

# VisualShaderNodeVectorDistance {#class_VisualShaderNodeVectorDistance}

**Inherits:**
`VisualShaderNodeVectorBase<class_VisualShaderNodeVectorBase>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Returns the distance between two points. To be used within the visual
shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Calculates distance from point represented by vector `p0` to vector
`p1`.

Translated to `distance(p0, p1)` in the shader language.
