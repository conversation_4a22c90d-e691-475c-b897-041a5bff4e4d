github_url

:   hide

# Texture {#class_Texture}

**Inherits:** `Resource<class_Resource>`{.interpreted-text role="ref"}
**\<** `RefCounted<class_RefCounted>`{.interpreted-text role="ref"}
**\<** `Object<class_Object>`{.interpreted-text role="ref"}

**Inherited By:** `Texture2D<class_Texture2D>`{.interpreted-text
role="ref"}, `Texture3D<class_Texture3D>`{.interpreted-text role="ref"},
`TextureLayered<class_TextureLayered>`{.interpreted-text role="ref"}

Base class for all texture types.

::: rst-class
classref-introduction-group
:::

## Description

**Texture** is the base class for all texture types. Common texture
types are `Texture2D<class_Texture2D>`{.interpreted-text role="ref"} and
`ImageTexture<class_ImageTexture>`{.interpreted-text role="ref"}. See
also `Image<class_Image>`{.interpreted-text role="ref"}.
