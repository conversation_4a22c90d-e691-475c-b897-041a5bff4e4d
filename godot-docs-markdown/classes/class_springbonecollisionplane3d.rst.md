github_url

:   hide

# SpringBoneCollisionPlane3D {#class_SpringBoneCollisionPlane3D}

**Inherits:**
`SpringBoneCollision3D<class_SpringBoneCollision3D>`{.interpreted-text
role="ref"} **\<** `Node3D<class_Node3D>`{.interpreted-text role="ref"}
**\<** `Node<class_Node>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A infinite plane collision that interacts with
`SpringBoneSimulator3D<class_SpringBoneSimulator3D>`{.interpreted-text
role="ref"}.

::: rst-class
classref-introduction-group
:::

## Description

A infinite plane collision that interacts with
`SpringBoneSimulator3D<class_SpringBoneSimulator3D>`{.interpreted-text
role="ref"}. It is an infinite size XZ plane, and the +Y direction is
treated as normal.
