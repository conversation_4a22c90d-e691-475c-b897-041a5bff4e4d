github_url

:   hide

# SphereOccluder3D {#class_SphereOccluder3D}

**Inherits:** `Occluder3D<class_Occluder3D>`{.interpreted-text
role="ref"} **\<** `Resource<class_Resource>`{.interpreted-text
role="ref"} **\<** `RefCounted<class_RefCounted>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

Spherical shape for use with occlusion culling in
`OccluderInstance3D<class_OccluderInstance3D>`{.interpreted-text
role="ref"}.

::: rst-class
classref-introduction-group
:::

## Description

**SphereOccluder3D** stores a sphere shape that can be used by the
engine\'s occlusion culling system.

See `OccluderInstance3D<class_OccluderInstance3D>`{.interpreted-text
role="ref"}\'s documentation for instructions on setting up occlusion
culling.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `Occlusion culling <../tutorials/3d/occlusion_culling>`{.interpreted-text
    role="doc"}

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Property Descriptions

::: {#class_SphereOccluder3D_property_radius}
::: rst-class
classref-property
:::
:::

`float<class_float>`{.interpreted-text role="ref"} **radius** = `1.0`
`🔗<class_SphereOccluder3D_property_radius>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_radius**(value: `float<class_float>`{.interpreted-text
    role="ref"})
-   `float<class_float>`{.interpreted-text role="ref"} **get_radius**()

The sphere\'s radius in 3D units.
