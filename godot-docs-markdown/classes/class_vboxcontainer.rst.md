github_url

:   hide

# VBoxContainer {#class_VBoxContainer}

**Inherits:** `BoxContainer<class_BoxContainer>`{.interpreted-text
role="ref"} **\<** `Container<class_Container>`{.interpreted-text
role="ref"} **\<** `Control<class_Control>`{.interpreted-text
role="ref"} **\<** `CanvasItem<class_CanvasItem>`{.interpreted-text
role="ref"} **\<** `Node<class_Node>`{.interpreted-text role="ref"}
**\<** `Object<class_Object>`{.interpreted-text role="ref"}

**Inherited By:** `ColorPicker<class_ColorPicker>`{.interpreted-text
role="ref"}, `FileSystemDock<class_FileSystemDock>`{.interpreted-text
role="ref"},
`ScriptEditorBase<class_ScriptEditorBase>`{.interpreted-text role="ref"}

A container that arranges its child controls vertically.

::: rst-class
classref-introduction-group
:::

## Description

A variant of `BoxContainer<class_BoxContainer>`{.interpreted-text
role="ref"} that can only arrange its child controls vertically. Child
controls are rearranged automatically when their minimum size changes.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `Using Containers <../tutorials/ui/gui_containers>`{.interpreted-text
    role="doc"}
-   [3D Voxel Demo](https://godotengine.org/asset-library/asset/2755)
