github_url

:   hide

# VisualShaderNodeCurveTexture {#class_VisualShaderNodeCurveTexture}

**Inherits:**
`VisualShaderNodeResizableBase<class_VisualShaderNodeResizableBase>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Performs a `CurveTexture<class_CurveTexture>`{.interpreted-text
role="ref"} lookup within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Comes with a built-in editor for texture\'s curves.

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Property Descriptions

::: {#class_VisualShaderNodeCurveTexture_property_texture}
::: rst-class
classref-property
:::
:::

`CurveTexture<class_CurveTexture>`{.interpreted-text role="ref"}
**texture**
`🔗<class_VisualShaderNodeCurveTexture_property_texture>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_texture**(value:
    `CurveTexture<class_CurveTexture>`{.interpreted-text role="ref"})
-   `CurveTexture<class_CurveTexture>`{.interpreted-text role="ref"}
    **get_texture**()

The source texture.
