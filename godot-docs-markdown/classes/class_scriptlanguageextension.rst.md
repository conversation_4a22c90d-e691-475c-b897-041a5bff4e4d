github_url

:   hide

# ScriptLanguageExtension {#class_ScriptLanguageExtension}

**Inherits:** `ScriptLanguage<class_ScriptLanguage>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

::: {.container .contribute}
There is currently no description for this class. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-reftable-group
:::

## Methods

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Enumerations

::: {#enum_ScriptLanguageExtension_LookupResultType}
::: rst-class
classref-enumeration
:::
:::

enum **LookupResultType**:
`🔗<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"}

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_SCRIPT_LOCATION}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_SCRIPT_LOCATION** = `0`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_CLASS}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_CLASS** = `1`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_CLASS_CONSTANT}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_CLASS_CONSTANT** = `2`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_CLASS_PROPERTY}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_CLASS_PROPERTY** = `3`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_CLASS_METHOD}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_CLASS_METHOD** = `4`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_CLASS_SIGNAL}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_CLASS_SIGNAL** = `5`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_CLASS_ENUM}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_CLASS_ENUM** = `6`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_CLASS_TBD_GLOBALSCOPE}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_CLASS_TBD_GLOBALSCOPE** = `7`

**Deprecated:** This constant may be changed or removed in future
versions.

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_CLASS_ANNOTATION}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_CLASS_ANNOTATION** = `8`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_LOCAL_CONSTANT}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_LOCAL_CONSTANT** = `9`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_LOCAL_VARIABLE}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_LOCAL_VARIABLE** = `10`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_LOOKUP_RESULT_MAX}
::: rst-class
classref-enumeration-constant
:::
:::

`LookupResultType<enum_ScriptLanguageExtension_LookupResultType>`{.interpreted-text
role="ref"} **LOOKUP_RESULT_MAX** = `11`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#enum_ScriptLanguageExtension_CodeCompletionLocation}
::: rst-class
classref-enumeration
:::
:::

enum **CodeCompletionLocation**:
`🔗<enum_ScriptLanguageExtension_CodeCompletionLocation>`{.interpreted-text
role="ref"}

::: {#class_ScriptLanguageExtension_constant_LOCATION_LOCAL}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionLocation<enum_ScriptLanguageExtension_CodeCompletionLocation>`{.interpreted-text
role="ref"} **LOCATION_LOCAL** = `0`

The option is local to the location of the code completion query - e.g.
a local variable. Subsequent value of location represent options from
the outer class, the exact value represent how far they are (in terms of
inner classes).

::: {#class_ScriptLanguageExtension_constant_LOCATION_PARENT_MASK}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionLocation<enum_ScriptLanguageExtension_CodeCompletionLocation>`{.interpreted-text
role="ref"} **LOCATION_PARENT_MASK** = `256`

The option is from the containing class or a parent class, relative to
the location of the code completion query. Perform a bitwise OR with the
class depth (e.g. `0` for the local class, `1` for the parent, `2` for
the grandparent, etc.) to store the depth of an option in the class or a
parent class.

::: {#class_ScriptLanguageExtension_constant_LOCATION_OTHER_USER_CODE}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionLocation<enum_ScriptLanguageExtension_CodeCompletionLocation>`{.interpreted-text
role="ref"} **LOCATION_OTHER_USER_CODE** = `512`

The option is from user code which is not local and not in a derived
class (e.g. Autoload Singletons).

::: {#class_ScriptLanguageExtension_constant_LOCATION_OTHER}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionLocation<enum_ScriptLanguageExtension_CodeCompletionLocation>`{.interpreted-text
role="ref"} **LOCATION_OTHER** = `1024`

The option is from other engine code, not covered by the other enum
constants - e.g. built-in classes.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#enum_ScriptLanguageExtension_CodeCompletionKind}
::: rst-class
classref-enumeration
:::
:::

enum **CodeCompletionKind**:
`🔗<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"}

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_CLASS}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_CLASS** = `0`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_FUNCTION}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_FUNCTION** = `1`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_SIGNAL}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_SIGNAL** = `2`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_VARIABLE}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_VARIABLE** = `3`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_MEMBER}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_MEMBER** = `4`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_ENUM}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_ENUM** = `5`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_CONSTANT}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_CONSTANT** = `6`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_NODE_PATH}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_NODE_PATH** = `7`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_FILE_PATH}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_FILE_PATH** = `8`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_PLAIN_TEXT}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_PLAIN_TEXT** = `9`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguageExtension_constant_CODE_COMPLETION_KIND_MAX}
::: rst-class
classref-enumeration-constant
:::
:::

`CodeCompletionKind<enum_ScriptLanguageExtension_CodeCompletionKind>`{.interpreted-text
role="ref"} **CODE_COMPLETION_KIND_MAX** = `10`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Method Descriptions

::: {#class_ScriptLanguageExtension_private_method__add_global_constant}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_add_global_constant**(name:
`StringName<class_StringName>`{.interpreted-text role="ref"}, value:
`Variant<class_Variant>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__add_global_constant>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__add_named_global_constant}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_add_named_global_constant**(name:
`StringName<class_StringName>`{.interpreted-text role="ref"}, value:
`Variant<class_Variant>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__add_named_global_constant>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__auto_indent_code}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**\_auto_indent_code**(code: `String<class_String>`{.interpreted-text
role="ref"}, from_line: `int<class_int>`{.interpreted-text role="ref"},
to_line: `int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__auto_indent_code>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__can_inherit_from_file}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"}
**\_can_inherit_from_file**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__can_inherit_from_file>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__can_make_function}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"}
**\_can_make_function**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__can_make_function>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__complete_code}
::: rst-class
classref-method
:::
:::

`Dictionary<class_Dictionary>`{.interpreted-text role="ref"}
**\_complete_code**(code: `String<class_String>`{.interpreted-text
role="ref"}, path: `String<class_String>`{.interpreted-text role="ref"},
owner: `Object<class_Object>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__complete_code>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__create_script}
::: rst-class
classref-method
:::
:::

`Object<class_Object>`{.interpreted-text role="ref"}
**\_create_script**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__create_script>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_get_current_stack_info}
::: rst-class
classref-method
:::
:::

`Array<class_Array>`{.interpreted-text
role="ref"}\[`Dictionary<class_Dictionary>`{.interpreted-text
role="ref"}\] **\_debug_get_current_stack_info**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_get_current_stack_info>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_get_error}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**\_debug_get_error**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_get_error>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_get_globals}
::: rst-class
classref-method
:::
:::

`Dictionary<class_Dictionary>`{.interpreted-text role="ref"}
**\_debug_get_globals**(max_subitems: `int<class_int>`{.interpreted-text
role="ref"}, max_depth: `int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_get_globals>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_get_stack_level_count}
::: rst-class
classref-method
:::
:::

`int<class_int>`{.interpreted-text role="ref"}
**\_debug_get_stack_level_count**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_get_stack_level_count>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_get_stack_level_function}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**\_debug_get_stack_level_function**(level:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_get_stack_level_function>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_get_stack_level_instance}
::: rst-class
classref-method
:::
:::

`void*` **\_debug_get_stack_level_instance**(level:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_get_stack_level_instance>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_get_stack_level_line}
::: rst-class
classref-method
:::
:::

`int<class_int>`{.interpreted-text role="ref"}
**\_debug_get_stack_level_line**(level:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_get_stack_level_line>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_get_stack_level_locals}
::: rst-class
classref-method
:::
:::

`Dictionary<class_Dictionary>`{.interpreted-text role="ref"}
**\_debug_get_stack_level_locals**(level:
`int<class_int>`{.interpreted-text role="ref"}, max_subitems:
`int<class_int>`{.interpreted-text role="ref"}, max_depth:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_get_stack_level_locals>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_get_stack_level_members}
::: rst-class
classref-method
:::
:::

`Dictionary<class_Dictionary>`{.interpreted-text role="ref"}
**\_debug_get_stack_level_members**(level:
`int<class_int>`{.interpreted-text role="ref"}, max_subitems:
`int<class_int>`{.interpreted-text role="ref"}, max_depth:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_get_stack_level_members>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_get_stack_level_source}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**\_debug_get_stack_level_source**(level:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_get_stack_level_source>`{.interpreted-text
role="ref"}

Returns the source associated with a given debug stack position.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__debug_parse_stack_level_expression}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**\_debug_parse_stack_level_expression**(level:
`int<class_int>`{.interpreted-text role="ref"}, expression:
`String<class_String>`{.interpreted-text role="ref"}, max_subitems:
`int<class_int>`{.interpreted-text role="ref"}, max_depth:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__debug_parse_stack_level_expression>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__find_function}
::: rst-class
classref-method
:::
:::

`int<class_int>`{.interpreted-text role="ref"}
**\_find_function**(function: `String<class_String>`{.interpreted-text
role="ref"}, code: `String<class_String>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__find_function>`{.interpreted-text
role="ref"}

Returns the line where the function is defined in the code, or `-1` if
the function is not present.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__finish}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"} **\_finish**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__finish>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__frame}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"} **\_frame**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__frame>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_built_in_templates}
::: rst-class
classref-method
:::
:::

`Array<class_Array>`{.interpreted-text
role="ref"}\[`Dictionary<class_Dictionary>`{.interpreted-text
role="ref"}\] **\_get_built_in_templates**(object:
`StringName<class_StringName>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_built_in_templates>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_comment_delimiters}
::: rst-class
classref-method
:::
:::

`PackedStringArray<class_PackedStringArray>`{.interpreted-text
role="ref"} **\_get_comment_delimiters**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_comment_delimiters>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_doc_comment_delimiters}
::: rst-class
classref-method
:::
:::

`PackedStringArray<class_PackedStringArray>`{.interpreted-text
role="ref"} **\_get_doc_comment_delimiters**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_doc_comment_delimiters>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_extension}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**\_get_extension**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_extension>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_global_class_name}
::: rst-class
classref-method
:::
:::

`Dictionary<class_Dictionary>`{.interpreted-text role="ref"}
**\_get_global_class_name**(path:
`String<class_String>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_global_class_name>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_name}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"} **\_get_name**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_name>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_public_annotations}
::: rst-class
classref-method
:::
:::

`Array<class_Array>`{.interpreted-text
role="ref"}\[`Dictionary<class_Dictionary>`{.interpreted-text
role="ref"}\] **\_get_public_annotations**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_public_annotations>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_public_constants}
::: rst-class
classref-method
:::
:::

`Dictionary<class_Dictionary>`{.interpreted-text role="ref"}
**\_get_public_constants**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_public_constants>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_public_functions}
::: rst-class
classref-method
:::
:::

`Array<class_Array>`{.interpreted-text
role="ref"}\[`Dictionary<class_Dictionary>`{.interpreted-text
role="ref"}\] **\_get_public_functions**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_public_functions>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_recognized_extensions}
::: rst-class
classref-method
:::
:::

`PackedStringArray<class_PackedStringArray>`{.interpreted-text
role="ref"} **\_get_recognized_extensions**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_recognized_extensions>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_reserved_words}
::: rst-class
classref-method
:::
:::

`PackedStringArray<class_PackedStringArray>`{.interpreted-text
role="ref"} **\_get_reserved_words**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_reserved_words>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_string_delimiters}
::: rst-class
classref-method
:::
:::

`PackedStringArray<class_PackedStringArray>`{.interpreted-text
role="ref"} **\_get_string_delimiters**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_string_delimiters>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__get_type}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"} **\_get_type**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__get_type>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__handles_global_class_type}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"}
**\_handles_global_class_type**(type:
`String<class_String>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__handles_global_class_type>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__has_named_classes}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"}
**\_has_named_classes**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__has_named_classes>`{.interpreted-text
role="ref"}

**Deprecated:** This method is not called by the engine.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__init}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"} **\_init**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__init>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__is_control_flow_keyword}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"}
**\_is_control_flow_keyword**(keyword:
`String<class_String>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__is_control_flow_keyword>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__is_using_templates}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"}
**\_is_using_templates**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__is_using_templates>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__lookup_code}
::: rst-class
classref-method
:::
:::

`Dictionary<class_Dictionary>`{.interpreted-text role="ref"}
**\_lookup_code**(code: `String<class_String>`{.interpreted-text
role="ref"}, symbol: `String<class_String>`{.interpreted-text
role="ref"}, path: `String<class_String>`{.interpreted-text role="ref"},
owner: `Object<class_Object>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__lookup_code>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__make_function}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**\_make_function**(class_name: `String<class_String>`{.interpreted-text
role="ref"}, function_name: `String<class_String>`{.interpreted-text
role="ref"}, function_args:
`PackedStringArray<class_PackedStringArray>`{.interpreted-text
role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__make_function>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__make_template}
::: rst-class
classref-method
:::
:::

`Script<class_Script>`{.interpreted-text role="ref"}
**\_make_template**(template: `String<class_String>`{.interpreted-text
role="ref"}, class_name: `String<class_String>`{.interpreted-text
role="ref"}, base_class_name: `String<class_String>`{.interpreted-text
role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__make_template>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__open_in_external_editor}
::: rst-class
classref-method
:::
:::

`Error<enum_@GlobalScope_Error>`{.interpreted-text role="ref"}
**\_open_in_external_editor**(script:
`Script<class_Script>`{.interpreted-text role="ref"}, line:
`int<class_int>`{.interpreted-text role="ref"}, column:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__open_in_external_editor>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__overrides_external_editor}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"}
**\_overrides_external_editor**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__overrides_external_editor>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__preferred_file_name_casing}
::: rst-class
classref-method
:::
:::

`ScriptNameCasing<enum_ScriptLanguage_ScriptNameCasing>`{.interpreted-text
role="ref"} **\_preferred_file_name_casing**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__preferred_file_name_casing>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__profiling_get_accumulated_data}
::: rst-class
classref-method
:::
:::

`int<class_int>`{.interpreted-text role="ref"}
**\_profiling_get_accumulated_data**(info_array:
`ScriptLanguageExtensionProfilingInfo*`, info_max:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__profiling_get_accumulated_data>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__profiling_get_frame_data}
::: rst-class
classref-method
:::
:::

`int<class_int>`{.interpreted-text role="ref"}
**\_profiling_get_frame_data**(info_array:
`ScriptLanguageExtensionProfilingInfo*`, info_max:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__profiling_get_frame_data>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__profiling_set_save_native_calls}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_profiling_set_save_native_calls**(enable:
`bool<class_bool>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__profiling_set_save_native_calls>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__profiling_start}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_profiling_start**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__profiling_start>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__profiling_stop}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_profiling_stop**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__profiling_stop>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__reload_all_scripts}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_reload_all_scripts**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__reload_all_scripts>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__reload_scripts}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_reload_scripts**(scripts: `Array<class_Array>`{.interpreted-text
role="ref"}, soft_reload: `bool<class_bool>`{.interpreted-text
role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__reload_scripts>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__reload_tool_script}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_reload_tool_script**(script:
`Script<class_Script>`{.interpreted-text role="ref"}, soft_reload:
`bool<class_bool>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__reload_tool_script>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__remove_named_global_constant}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_remove_named_global_constant**(name:
`StringName<class_StringName>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__remove_named_global_constant>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__supports_builtin_mode}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"}
**\_supports_builtin_mode**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__supports_builtin_mode>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__supports_documentation}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"}
**\_supports_documentation**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__supports_documentation>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__thread_enter}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_thread_enter**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__thread_enter>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__thread_exit}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_thread_exit**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__thread_exit>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__validate}
::: rst-class
classref-method
:::
:::

`Dictionary<class_Dictionary>`{.interpreted-text role="ref"}
**\_validate**(script: `String<class_String>`{.interpreted-text
role="ref"}, path: `String<class_String>`{.interpreted-text role="ref"},
validate_functions: `bool<class_bool>`{.interpreted-text role="ref"},
validate_errors: `bool<class_bool>`{.interpreted-text role="ref"},
validate_warnings: `bool<class_bool>`{.interpreted-text role="ref"},
validate_safe_lines: `bool<class_bool>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__validate>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_ScriptLanguageExtension_private_method__validate_path}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**\_validate_path**(path: `String<class_String>`{.interpreted-text
role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_ScriptLanguageExtension_private_method__validate_path>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::
