github_url

:   hide

allow_comments

:   False

# All classes {#doc_class_reference}

# Globals

::: {#toc-class-ref-globals .toctree maxdepth="1"}
[class]()\@gdscript [class]()\@globalscope
:::

# Nodes

::: {#toc-class-ref-nodes .toctree maxdepth="1"}
class_node class_acceptdialog class_animatablebody2d
class_animatablebody3d class_animatedsprite2d class_animatedsprite3d
class_animationmixer class_animationplayer class_animationtree
class_area2d class_area3d class_aspectratiocontainer
class_audiolistener2d class_audiolistener3d class_audiostreamplayer
class_audiostreamplayer2d class_audiostreamplayer3d class_backbuffercopy
class_basebutton class_bone2d class_boneattachment3d class_boxcontainer
class_button class_camera2d class_camera3d class_canvasgroup
class_canvasitem class_canvaslayer class_canvasmodulate
class_centercontainer class_characterbody2d class_characterbody3d
class_checkbox class_checkbutton class_codeedit class_collisionobject2d
class_collisionobject3d class_collisionpolygon2d
class_collisionpolygon3d class_collisionshape2d class_collisionshape3d
class_colorpicker class_colorpickerbutton class_colorrect
class_conetwistjoint3d class_confirmationdialog class_container
class_control class_cpuparticles2d class_cpuparticles3d class_csgbox3d
class_csgcombiner3d class_csgcylinder3d class_csgmesh3d
class_csgpolygon3d class_csgprimitive3d class_csgshape3d
class_csgsphere3d class_csgtorus3d class_dampedspringjoint2d class_decal
class_directionallight2d class_directionallight3d
class_editorcommandpalette class_editorfiledialog class_editorfilesystem
class_editorinspector class_editorplugin class_editorproperty
class_editorresourcepicker class_editorresourcepreview
class_editorscriptpicker class_editorspinslider class_editortoaster
class_filedialog class_filesystemdock class_flowcontainer
class_fogvolume class_generic6dofjoint3d class_geometryinstance3d
class_gpuparticles2d class_gpuparticles3d class_gpuparticlesattractor3d
class_gpuparticlesattractorbox3d class_gpuparticlesattractorsphere3d
class_gpuparticlesattractorvectorfield3d class_gpuparticlescollision3d
class_gpuparticlescollisionbox3d
class_gpuparticlescollisionheightfield3d
class_gpuparticlescollisionsdf3d class_gpuparticlescollisionsphere3d
class_graphedit class_graphelement class_graphframe class_graphnode
class_gridcontainer class_gridmap class_gridmapeditorplugin
class_groovejoint2d class_hboxcontainer class_hflowcontainer
class_hingejoint3d class_hscrollbar class_hseparator class_hslider
class_hsplitcontainer class_httprequest class_importermeshinstance3d
class_instanceplaceholder class_itemlist class_joint2d class_joint3d
class_label class_label3d class_light2d class_light3d class_lightmapgi
class_lightmapprobe class_lightoccluder2d class_line2d class_lineedit
class_linkbutton class_lookatmodifier3d class_margincontainer
class_marker2d class_marker3d class_menubar class_menubutton
class_meshinstance2d class_meshinstance3d class_missingnode
class_multimeshinstance2d class_multimeshinstance3d
class_multiplayerspawner class_multiplayersynchronizer
class_navigationagent2d class_navigationagent3d class_navigationlink2d
class_navigationlink3d class_navigationobstacle2d
class_navigationobstacle3d class_navigationregion2d
class_navigationregion3d class_ninepatchrect class_node2d class_node3d
class_occluderinstance3d class_omnilight3d
class_openxrbindingmodifiereditor class_openxrcompositionlayer
class_openxrcompositionlayercylinder
class_openxrcompositionlayerequirect class_openxrcompositionlayerquad
class_openxrhand class_openxrinteractionprofileeditor
class_openxrinteractionprofileeditorbase class_openxrvisibilitymask
class_optionbutton class_panel class_panelcontainer class_parallax2d
class_parallaxbackground class_parallaxlayer class_path2d class_path3d
class_pathfollow2d class_pathfollow3d class_physicalbone2d
class_physicalbone3d class_physicalbonesimulator3d class_physicsbody2d
class_physicsbody3d class_pinjoint2d class_pinjoint3d class_pointlight2d
class_polygon2d class_popup class_popupmenu class_popuppanel
class_progressbar class_range class_raycast2d class_raycast3d
class_referencerect class_reflectionprobe class_remotetransform2d
class_remotetransform3d class_resourcepreloader class_retargetmodifier3d
class_richtextlabel class_rigidbody2d class_rigidbody3d
class_rootmotionview class_scriptcreatedialog class_scripteditor
class_scripteditorbase class_scrollbar class_scrollcontainer
class_separator class_shaderglobalsoverride class_shapecast2d
class_shapecast3d class_skeleton2d class_skeleton3d class_skeletonik3d
class_skeletonmodifier3d class_slider class_sliderjoint3d
class_softbody3d class_spinbox class_splitcontainer class_spotlight3d
class_springarm3d class_springbonecollision3d
class_springbonecollisioncapsule3d class_springbonecollisionplane3d
class_springbonecollisionsphere3d class_springbonesimulator3d
class_sprite2d class_sprite3d class_spritebase3d class_staticbody2d
class_staticbody3d class_statusindicator class_subviewport
class_subviewportcontainer class_tabbar class_tabcontainer
class_textedit class_texturebutton class_textureprogressbar
class_texturerect class_tilemap class_tilemaplayer class_timer
class_touchscreenbutton class_tree class_vboxcontainer
class_vehiclebody3d class_vehiclewheel3d class_vflowcontainer
class_videostreamplayer class_viewport class_visibleonscreenenabler2d
class_visibleonscreenenabler3d class_visibleonscreennotifier2d
class_visibleonscreennotifier3d class_visualinstance3d class_voxelgi
class_vscrollbar class_vseparator class_vslider class_vsplitcontainer
class_window class_worldenvironment class_xranchor3d
class_xrbodymodifier3d class_xrcamera3d class_xrcontroller3d
class_xrfacemodifier3d class_xrhandmodifier3d class_xrnode3d
class_xrorigin3d
:::

# Resources

::: {#toc-class-ref-resources .toctree maxdepth="1"}
class_resource class_animatedtexture class_animation
class_animationlibrary class_animationnode class_animationnodeadd2
class_animationnodeadd3 class_animationnodeanimation
class_animationnodeblend2 class_animationnodeblend3
class_animationnodeblendspace1d class_animationnodeblendspace2d
class_animationnodeblendtree class_animationnodeextension
class_animationnodeoneshot class_animationnodeoutput
class_animationnodestatemachine class_animationnodestatemachineplayback
class_animationnodestatemachinetransition class_animationnodesub2
class_animationnodesync class_animationnodetimescale
class_animationnodetimeseek class_animationnodetransition
class_animationrootnode class_arraymesh class_arrayoccluder3d
class_atlastexture class_audiobuslayout class_audioeffect
class_audioeffectamplify class_audioeffectbandlimitfilter
class_audioeffectbandpassfilter class_audioeffectcapture
class_audioeffectchorus class_audioeffectcompressor
class_audioeffectdelay class_audioeffectdistortion class_audioeffecteq
class_audioeffecteq10 class_audioeffecteq21 class_audioeffecteq6
class_audioeffectfilter class_audioeffecthardlimiter
class_audioeffecthighpassfilter class_audioeffecthighshelffilter
class_audioeffectlimiter class_audioeffectlowpassfilter
class_audioeffectlowshelffilter class_audioeffectnotchfilter
class_audioeffectpanner class_audioeffectphaser
class_audioeffectpitchshift class_audioeffectrecord
class_audioeffectreverb class_audioeffectspectrumanalyzer
class_audioeffectstereoenhance class_audiostream
class_audiostreamgenerator class_audiostreaminteractive
class_audiostreammicrophone class_audiostreammp3
class_audiostreamoggvorbis class_audiostreamplaylist
class_audiostreampolyphonic class_audiostreamrandomizer
class_audiostreamsynchronized class_audiostreamwav class_basematerial3d
class_bitmap class_bonemap class_boxmesh class_boxoccluder3d
class_boxshape3d class_buttongroup class_cameraattributes
class_cameraattributesphysical class_cameraattributespractical
class_cameratexture class_canvasitemmaterial class_canvastexture
class_capsulemesh class_capsuleshape2d class_capsuleshape3d
class_circleshape2d class_codehighlighter class_colorpalette
class_compositor class_compositoreffect class_compressedcubemap
class_compressedcubemaparray class_compressedtexture2d
class_compressedtexture2darray class_compressedtexture3d
class_compressedtexturelayered class_concavepolygonshape2d
class_concavepolygonshape3d class_convexpolygonshape2d
class_convexpolygonshape3d class_cryptokey class_csharpscript
class_cubemap class_cubemaparray class_curve class_curve2d class_curve3d
class_curvetexture class_curvexyztexture class_cylindermesh
class_cylindershape3d class_editornode3dgizmoplugin class_editorsettings
class_editorsyntaxhighlighter class_environment class_externaltexture
class_fastnoiselite class_fbxdocument class_fbxstate class_fogmaterial
class_font class_fontfile class_fontvariation class_gdextension
class_gdscript class_gdscriptsyntaxhighlighter class_gltfaccessor
class_gltfanimation class_gltfbufferview class_gltfcamera
class_gltfdocument class_gltfdocumentextension
class_gltfdocumentextensionconvertimportermesh class_gltflight
class_gltfmesh class_gltfnode class_gltfphysicsbody
class_gltfphysicsshape class_gltfskeleton class_gltfskin
class_gltfspecgloss class_gltfstate class_gltftexture
class_gltftexturesampler class_gradient class_gradienttexture1d
class_gradienttexture2d class_heightmapshape3d class_image
class_imagetexture class_imagetexture3d class_imagetexturelayered
class_immediatemesh class_importermesh class_inputevent
class_inputeventaction class_inputeventfromwindow
class_inputeventgesture class_inputeventjoypadbutton
class_inputeventjoypadmotion class_inputeventkey
class_inputeventmagnifygesture class_inputeventmidi
class_inputeventmouse class_inputeventmousebutton
class_inputeventmousemotion class_inputeventpangesture
class_inputeventscreendrag class_inputeventscreentouch
class_inputeventshortcut class_inputeventwithmodifiers class_json
class_labelsettings class_lightmapgidata class_material class_mesh
class_meshlibrary class_meshtexture class_missingresource
class_multimesh class_navigationmesh
class_navigationmeshsourcegeometrydata2d
class_navigationmeshsourcegeometrydata3d class_navigationpolygon
class_noise class_noisetexture2d class_noisetexture3d class_occluder3d
class_occluderpolygon2d class_oggpacketsequence class_openxraction
class_openxractionbindingmodifier class_openxractionmap
class_openxractionset class_openxranalogthresholdmodifier
class_openxrbindingmodifier class_openxrdpadbindingmodifier
class_openxrhapticbase class_openxrhapticvibration
class_openxrinteractionprofile class_openxripbinding
class_openxripbindingmodifier class_optimizedtranslation
class_ormmaterial3d class_packeddatacontainer class_packedscene
class_panoramaskymaterial class_particleprocessmaterial
class_physicalskymaterial class_physicsmaterial class_placeholdercubemap
class_placeholdercubemaparray class_placeholdermaterial
class_placeholdermesh class_placeholdertexture2d
class_placeholdertexture2darray class_placeholdertexture3d
class_placeholdertexturelayered class_planemesh class_pointmesh
class_polygonoccluder3d class_polygonpathfinder
class_portablecompressedtexture2d class_primitivemesh class_prismmesh
class_proceduralskymaterial class_quadmesh class_quadoccluder3d
class_rdshaderfile class_rdshaderspirv class_rectangleshape2d
class_ribbontrailmesh class_richtexteffect class_scenereplicationconfig
class_script class_scriptextension class_segmentshape2d
class_separationrayshape2d class_separationrayshape3d class_shader
class_shaderinclude class_shadermaterial class_shape2d class_shape3d
class_shortcut class_skeletonmodification2d
class_skeletonmodification2dccdik class_skeletonmodification2dfabrik
class_skeletonmodification2djiggle class_skeletonmodification2dlookat
class_skeletonmodification2dphysicalbones
class_skeletonmodification2dstackholder
class_skeletonmodification2dtwoboneik class_skeletonmodificationstack2d
class_skeletonprofile class_skeletonprofilehumanoid class_skin class_sky
class_spheremesh class_sphereoccluder3d class_sphereshape3d
class_spriteframes class_standardmaterial3d class_stylebox
class_styleboxempty class_styleboxflat class_styleboxline
class_styleboxtexture class_syntaxhighlighter class_systemfont
class_textmesh class_texture class_texture2d class_texture2darray
class_texture2darrayrd class_texture2drd class_texture3d
class_texture3drd class_texturecubemaparrayrd class_texturecubemaprd
class_texturelayered class_texturelayeredrd class_theme
class_tilemappattern class_tileset class_tilesetatlassource
class_tilesetscenescollectionsource class_tilesetsource class_torusmesh
class_translation class_tubetrailmesh class_videostream
class_videostreamplayback class_videostreamtheora class_viewporttexture
class_visualshader class_visualshadernode
class_visualshadernodebillboard class_visualshadernodebooleanconstant
class_visualshadernodebooleanparameter class_visualshadernodeclamp
class_visualshadernodecolorconstant class_visualshadernodecolorfunc
class_visualshadernodecolorop class_visualshadernodecolorparameter
class_visualshadernodecomment class_visualshadernodecompare
class_visualshadernodeconstant class_visualshadernodecubemap
class_visualshadernodecubemapparameter
class_visualshadernodecurvetexture class_visualshadernodecurvexyztexture
class_visualshadernodecustom class_visualshadernodederivativefunc
class_visualshadernodedeterminant class_visualshadernodedistancefade
class_visualshadernodedotproduct class_visualshadernodeexpression
class_visualshadernodefaceforward class_visualshadernodefloatconstant
class_visualshadernodefloatfunc class_visualshadernodefloatop
class_visualshadernodefloatparameter class_visualshadernodeframe
class_visualshadernodefresnel class_visualshadernodeglobalexpression
class_visualshadernodegroupbase class_visualshadernodeif
class_visualshadernodeinput class_visualshadernodeintconstant
class_visualshadernodeintfunc class_visualshadernodeintop
class_visualshadernodeintparameter class_visualshadernodeis
class_visualshadernodelinearscenedepth class_visualshadernodemix
class_visualshadernodemultiplyadd class_visualshadernodeouterproduct
class_visualshadernodeoutput class_visualshadernodeparameter
class_visualshadernodeparameterref
class_visualshadernodeparticleaccelerator
class_visualshadernodeparticleboxemitter
class_visualshadernodeparticleconevelocity
class_visualshadernodeparticleemit class_visualshadernodeparticleemitter
class_visualshadernodeparticlemeshemitter
class_visualshadernodeparticlemultiplybyaxisangle
class_visualshadernodeparticleoutput
class_visualshadernodeparticlerandomness
class_visualshadernodeparticleringemitter
class_visualshadernodeparticlesphereemitter
class_visualshadernodeproximityfade class_visualshadernoderandomrange
class_visualshadernoderemap class_visualshadernodereroute
class_visualshadernoderesizablebase class_visualshadernoderotationbyaxis
class_visualshadernodesample3d
class_visualshadernodescreennormalworldspace
class_visualshadernodescreenuvtosdf class_visualshadernodesdfraymarch
class_visualshadernodesdftoscreenuv class_visualshadernodesmoothstep
class_visualshadernodestep class_visualshadernodeswitch
class_visualshadernodetexture class_visualshadernodetexture2darray
class_visualshadernodetexture2darrayparameter
class_visualshadernodetexture2dparameter class_visualshadernodetexture3d
class_visualshadernodetexture3dparameter
class_visualshadernodetextureparameter
class_visualshadernodetextureparametertriplanar
class_visualshadernodetexturesdf class_visualshadernodetexturesdfnormal
class_visualshadernodetransformcompose
class_visualshadernodetransformconstant
class_visualshadernodetransformdecompose
class_visualshadernodetransformfunc class_visualshadernodetransformop
class_visualshadernodetransformparameter
class_visualshadernodetransformvecmult
class_visualshadernodeuintconstant class_visualshadernodeuintfunc
class_visualshadernodeuintop class_visualshadernodeuintparameter
class_visualshadernodeuvfunc class_visualshadernodeuvpolarcoord
class_visualshadernodevarying class_visualshadernodevaryinggetter
class_visualshadernodevaryingsetter class_visualshadernodevec2constant
class_visualshadernodevec2parameter class_visualshadernodevec3constant
class_visualshadernodevec3parameter class_visualshadernodevec4constant
class_visualshadernodevec4parameter class_visualshadernodevectorbase
class_visualshadernodevectorcompose
class_visualshadernodevectordecompose
class_visualshadernodevectordistance class_visualshadernodevectorfunc
class_visualshadernodevectorlen class_visualshadernodevectorop
class_visualshadernodevectorrefract
class_visualshadernodeworldpositionfromdepth class_voxelgidata
class_world2d class_world3d class_worldboundaryshape2d
class_worldboundaryshape3d class_x509certificate
:::

# Other objects

::: {#toc-class-ref-objects .toctree maxdepth="1"}
class_object class_aescontext class_astar2d class_astar3d
class_astargrid2d class_audioeffectinstance
class_audioeffectspectrumanalyzerinstance class_audiosample
class_audiosampleplayback class_audioserver
class_audiostreamgeneratorplayback class_audiostreamplayback
class_audiostreamplaybackinteractive class_audiostreamplaybackoggvorbis
class_audiostreamplaybackplaylist class_audiostreamplaybackpolyphonic
class_audiostreamplaybackresampled class_audiostreamplaybacksynchronized
class_callbacktweener class_camerafeed class_cameraserver
class_charfxtransform class_classdb class_configfile class_crypto
class_diraccess class_displayserver class_dtlsserver
class_editorcontextmenuplugin class_editordebuggerplugin
class_editordebuggersession class_editorexportplatform
class_editorexportplatformandroid class_editorexportplatformextension
class_editorexportplatformios class_editorexportplatformlinuxbsd
class_editorexportplatformmacos class_editorexportplatformpc
class_editorexportplatformweb class_editorexportplatformwindows
class_editorexportplugin class_editorexportpreset
class_editorfeatureprofile class_editorfilesystemdirectory
class_editorfilesystemimportformatsupportquery class_editorimportplugin
class_editorinspectorplugin class_editorinterface
class_editornode3dgizmo class_editorpaths
class_editorresourceconversionplugin
class_editorresourcepreviewgenerator class_editorresourcetooltipplugin
class_editorsceneformatimporter class_editorsceneformatimporterblend
class_editorsceneformatimporterfbx2gltf
class_editorsceneformatimportergltf class_editorsceneformatimporterufbx
class_editorscenepostimport class_editorscenepostimportplugin
class_editorscript class_editorselection
class_editortranslationparserplugin class_editorundoredomanager
class_editorvcsinterface class_encodedobjectasid class_enetconnection
class_enetmultiplayerpeer class_enetpacketpeer class_engine
class_enginedebugger class_engineprofiler class_expression
class_fileaccess class_framebuffercacherd class_gdextensionmanager
class_geometry2d class_geometry3d class_gltfobjectmodelproperty
class_hashingcontext class_hmaccontext class_httpclient
class_imageformatloader class_imageformatloaderextension class_input
class_inputmap class_intervaltweener class_ip class_javaclass
class_javaclasswrapper class_javaobject class_javascriptbridge
class_javascriptobject class_jnisingleton class_jsonrpc
class_kinematiccollision2d class_kinematiccollision3d class_lightmapper
class_lightmapperrd class_mainloop class_marshalls
class_meshconvexdecompositionsettings class_meshdatatool
class_methodtweener class_mobilevrinterface class_moviewriter
class_multiplayerapi class_multiplayerapiextension class_multiplayerpeer
class_multiplayerpeerextension class_mutex class_nativemenu
class_navigationmeshgenerator class_navigationpathqueryparameters2d
class_navigationpathqueryparameters3d class_navigationpathqueryresult2d
class_navigationpathqueryresult3d class_navigationserver2d
class_navigationserver3d class_node class_node3dgizmo
class_offlinemultiplayerpeer class_oggpacketsequenceplayback
class_openxrapiextension class_openxrextensionwrapperextension
class_openxrinteractionprofilemetadata class_openxrinterface class_os
class_packeddatacontainerref class_packetpeer class_packetpeerdtls
class_packetpeerextension class_packetpeerstream class_packetpeerudp
class_pckpacker class_performance class_physicsdirectbodystate2d
class_physicsdirectbodystate2dextension class_physicsdirectbodystate3d
class_physicsdirectbodystate3dextension class_physicsdirectspacestate2d
class_physicsdirectspacestate2dextension class_physicsdirectspacestate3d
class_physicsdirectspacestate3dextension
class_physicspointqueryparameters2d class_physicspointqueryparameters3d
class_physicsrayqueryparameters2d class_physicsrayqueryparameters3d
class_physicsserver2d class_physicsserver2dextension
class_physicsserver2dmanager class_physicsserver3d
class_physicsserver3dextension class_physicsserver3dmanager
class_physicsserver3drenderingserverhandler
class_physicsshapequeryparameters2d class_physicsshapequeryparameters3d
class_physicstestmotionparameters2d class_physicstestmotionparameters3d
class_physicstestmotionresult2d class_physicstestmotionresult3d
class_projectsettings class_propertytweener class_randomnumbergenerator
class_rdattachmentformat class_rdframebufferpass
class_rdpipelinecolorblendstate
class_rdpipelinecolorblendstateattachment
class_rdpipelinedepthstencilstate class_rdpipelinemultisamplestate
class_rdpipelinerasterizationstate
class_rdpipelinespecializationconstant class_rdsamplerstate
class_rdshadersource class_rdtextureformat class_rdtextureview
class_rduniform class_rdvertexattribute class_refcounted class_regex
class_regexmatch class_renderdata class_renderdataextension
class_renderdatard class_renderingdevice class_renderingserver
class_renderscenebuffers class_renderscenebuffersconfiguration
class_renderscenebuffersextension class_renderscenebuffersrd
class_renderscenedata class_renderscenedataextension
class_renderscenedatard class_resource class_resourceformatloader
class_resourceformatsaver class_resourceimporter
class_resourceimporterbitmap class_resourceimporterbmfont
class_resourceimportercsvtranslation class_resourceimporterdynamicfont
class_resourceimporterimage class_resourceimporterimagefont
class_resourceimporterlayeredtexture class_resourceimportermp3
class_resourceimporterobj class_resourceimporteroggvorbis
class_resourceimporterscene class_resourceimportershaderfile
class_resourceimportertexture class_resourceimportertextureatlas
class_resourceimporterwav class_resourceloader class_resourcesaver
class_resourceuid class_scenemultiplayer class_scenestate
class_scenetree class_scenetreetimer class_scriptlanguage
class_scriptlanguageextension class_semaphore class_shaderincludedb
class_skinreference class_streampeer class_streampeerbuffer
class_streampeerextension class_streampeergzip class_streampeertcp
class_streampeertls class_subtweentweener class_surfacetool
class_tcpserver class_textline class_textparagraph class_textserver
class_textserveradvanced class_textserverdummy class_textserverextension
class_textserverfallback class_textservermanager class_themedb
class_thread class_tiledata class_time class_tlsoptions
class_translationdomain class_translationserver class_treeitem
class_trianglemesh class_tween class_tweener class_udpserver
class_undoredo class_uniformsetcacherd class_upnp class_upnpdevice
class_weakref class_webrtcdatachannel class_webrtcdatachannelextension
class_webrtcmultiplayerpeer class_webrtcpeerconnection
class_webrtcpeerconnectionextension class_websocketmultiplayerpeer
class_websocketpeer class_webxrinterface class_workerthreadpool
class_xmlparser class_xrbodytracker class_xrcontrollertracker
class_xrfacetracker class_xrhandtracker class_xrinterface
class_xrinterfaceextension class_xrpose class_xrpositionaltracker
class_xrserver class_xrtracker class_xrvrs class_zippacker
class_zipreader
:::

# Editor-only

::: {#toc-class-ref-editors .toctree maxdepth="1"}
class_editorcommandpalette class_editorcontextmenuplugin
class_editordebuggerplugin class_editordebuggersession
class_editorexportplatform class_editorexportplatformandroid
class_editorexportplatformextension class_editorexportplatformios
class_editorexportplatformlinuxbsd class_editorexportplatformmacos
class_editorexportplatformpc class_editorexportplatformweb
class_editorexportplatformwindows class_editorexportplugin
class_editorexportpreset class_editorfeatureprofile
class_editorfiledialog class_editorfilesystem
class_editorfilesystemdirectory
class_editorfilesystemimportformatsupportquery class_editorimportplugin
class_editorinspector class_editorinspectorplugin class_editorinterface
class_editornode3dgizmo class_editornode3dgizmoplugin class_editorpaths
class_editorplugin class_editorproperty
class_editorresourceconversionplugin class_editorresourcepicker
class_editorresourcepreview class_editorresourcepreviewgenerator
class_editorresourcetooltipplugin class_editorsceneformatimporter
class_editorsceneformatimporterblend
class_editorsceneformatimporterfbx2gltf
class_editorsceneformatimportergltf class_editorsceneformatimporterufbx
class_editorscenepostimport class_editorscenepostimportplugin
class_editorscript class_editorscriptpicker class_editorselection
class_editorsettings class_editorspinslider
class_editorsyntaxhighlighter class_editortoaster
class_editortranslationparserplugin class_editorundoredomanager
class_editorvcsinterface class_filesystemdock class_scriptcreatedialog
class_scripteditor class_scripteditorbase
:::

# Variant types

::: {#toc-class-ref-variants .toctree maxdepth="1"}
class_variant class_aabb class_array class_basis class_bool
class_callable class_color class_dictionary class_float class_int
class_nodepath class_object class_packedbytearray class_packedcolorarray
class_packedfloat32array class_packedfloat64array class_packedint32array
class_packedint64array class_packedstringarray class_packedvector2array
class_packedvector3array class_packedvector4array class_plane
class_projection class_quaternion class_rect2 class_rect2i class_rid
class_signal class_string class_stringname class_transform2d
class_transform3d class_vector2 class_vector2i class_vector3
class_vector3i class_vector4 class_vector4i
:::
