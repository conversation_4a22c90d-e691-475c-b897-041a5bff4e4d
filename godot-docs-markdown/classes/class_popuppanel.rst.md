github_url

:   hide

# PopupPanel {#class_PopupPanel}

**Inherits:** `Popup<class_Popup>`{.interpreted-text role="ref"} **\<**
`Window<class_Window>`{.interpreted-text role="ref"} **\<**
`Viewport<class_Viewport>`{.interpreted-text role="ref"} **\<**
`Node<class_Node>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A popup with a panel background.

::: rst-class
classref-introduction-group
:::

## Description

A popup with a configurable panel background. Any child controls added
to this node will be stretched to fit the panel\'s size (similar to how
`PanelContainer<class_PanelContainer>`{.interpreted-text role="ref"}
works). If you are making windows, see
`Window<class_Window>`{.interpreted-text role="ref"}.

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-reftable-group
:::

## Theme Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Theme Property Descriptions

::: {#class_PopupPanel_theme_style_panel}
::: rst-class
classref-themeproperty
:::
:::

`StyleBox<class_StyleBox>`{.interpreted-text role="ref"} **panel**
`🔗<class_PopupPanel_theme_style_panel>`{.interpreted-text role="ref"}

`StyleBox<class_StyleBox>`{.interpreted-text role="ref"} for the
background panel.
