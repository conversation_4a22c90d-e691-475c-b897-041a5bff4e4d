github_url

:   hide

# VisualShaderNodeFresnel {#class_VisualShaderNodeFresnel}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A Fresnel effect to be used within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Returns falloff based on the dot product of surface normal and view
direction of camera (pass associated inputs to it).
