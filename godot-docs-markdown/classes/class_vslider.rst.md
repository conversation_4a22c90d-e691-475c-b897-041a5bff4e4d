github_url

:   hide

# VSlider {#class_VSlider}

**Inherits:** `Slider<class_Slider>`{.interpreted-text role="ref"}
**\<** `Range<class_Range>`{.interpreted-text role="ref"} **\<**
`Control<class_Control>`{.interpreted-text role="ref"} **\<**
`CanvasItem<class_CanvasItem>`{.interpreted-text role="ref"} **\<**
`Node<class_Node>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A vertical slider that goes from bottom (min) to top (max).

::: rst-class
classref-introduction-group
:::

## Description

A vertical slider, used to adjust a value by moving a grabber along a
vertical axis. It is a `Range<class_Range>`{.interpreted-text
role="ref"}-based control and goes from bottom (min) to top (max). Note
that this direction is the opposite of
`VScrollBar<class_VScrollBar>`{.interpreted-text role="ref"}\'s.

::: rst-class
classref-reftable-group
:::

## Properties

