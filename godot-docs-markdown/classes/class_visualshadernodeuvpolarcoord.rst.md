github_url

:   hide

# VisualShaderNodeUVPolarCoord {#class_VisualShaderNodeUVPolarCoord}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A visual shader node that modifies the texture UV using polar
coordinates.

::: rst-class
classref-introduction-group
:::

## Description

UV polar coord node will transform UV values into polar coordinates,
with specified scale, zoom strength and repeat parameters. It can be
used to create various swirl distortions.
