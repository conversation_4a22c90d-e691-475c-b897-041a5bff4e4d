github_url

:   hide

# Popup {#class_Popup}

**Inherits:** `Window<class_Window>`{.interpreted-text role="ref"}
**\<** `Viewport<class_Viewport>`{.interpreted-text role="ref"} **\<**
`Node<class_Node>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

**Inherited By:** `PopupMenu<class_PopupMenu>`{.interpreted-text
role="ref"}, `PopupPanel<class_PopupPanel>`{.interpreted-text
role="ref"}

Base class for contextual windows and panels with fixed position.

::: rst-class
classref-introduction-group
:::

## Description

**Popup** is a base class for contextual windows and panels with fixed
position. It\'s a modal by default (see
`Window.popup_window<class_Window_property_popup_window>`{.interpreted-text
role="ref"}) and provides methods for implementing custom popup
behavior.

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Signals

::: {#class_Popup_signal_popup_hide}
::: rst-class
classref-signal
:::
:::

**popup_hide**() `🔗<class_Popup_signal_popup_hide>`{.interpreted-text
role="ref"}

Emitted when the popup is hidden.
