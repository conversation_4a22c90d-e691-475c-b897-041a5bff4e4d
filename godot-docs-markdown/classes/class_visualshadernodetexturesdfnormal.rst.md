github_url

:   hide

# VisualShaderNodeTextureSDFNormal {#class_VisualShaderNodeTextureSDFNormal}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Performs an SDF (signed-distance field) normal texture lookup within the
visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Translates to `texture_sdf_normal(sdf_pos)` in the shader language.
