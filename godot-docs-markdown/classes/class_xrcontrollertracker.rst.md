github_url

:   hide

# XRControllerTracker {#class_XRControllerTracker}

**Inherits:**
`XRPositionalTracker<class_XRPositionalTracker>`{.interpreted-text
role="ref"} **\<** `XRTracker<class_XRTracker>`{.interpreted-text
role="ref"} **\<** `RefCounted<class_RefCounted>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

A tracked controller.

::: rst-class
classref-introduction-group
:::

## Description

An instance of this object represents a controller that is tracked.

As controllers are turned on and the
`XRInterface<class_XRInterface>`{.interpreted-text role="ref"} detects
them, instances of this object are automatically added to this list of
active tracking objects accessible through the
`XRServer<class_XRServer>`{.interpreted-text role="ref"}.

The `XRController3D<class_XRController3D>`{.interpreted-text role="ref"}
consumes objects of this type and should be used in your project.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `XR documentation index <../tutorials/xr/index>`{.interpreted-text
    role="doc"}

::: rst-class
classref-reftable-group
:::

## Properties

