github_url

:   hide

# PanelContainer {#class_PanelContainer}

**Inherits:** `Container<class_Container>`{.interpreted-text role="ref"}
**\<** `Control<class_Control>`{.interpreted-text role="ref"} **\<**
`CanvasItem<class_CanvasItem>`{.interpreted-text role="ref"} **\<**
`Node<class_Node>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

**Inherited By:**
`OpenXRBindingModifierEditor<class_OpenXRBindingModifierEditor>`{.interpreted-text
role="ref"}, `ScriptEditor<class_ScriptEditor>`{.interpreted-text
role="ref"}

A container that keeps its child controls within the area of a
`StyleBox<class_StyleBox>`{.interpreted-text role="ref"}.

::: rst-class
classref-introduction-group
:::

## Description

A container that keeps its child controls within the area of a
`StyleBox<class_StyleBox>`{.interpreted-text role="ref"}. Useful for
giving controls an outline.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `Using Containers <../tutorials/ui/gui_containers>`{.interpreted-text
    role="doc"}
-   [2D Role Playing Game (RPG)
    Demo](https://godotengine.org/asset-library/asset/2729)

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-reftable-group
:::

## Theme Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Theme Property Descriptions

::: {#class_PanelContainer_theme_style_panel}
::: rst-class
classref-themeproperty
:::
:::

`StyleBox<class_StyleBox>`{.interpreted-text role="ref"} **panel**
`🔗<class_PanelContainer_theme_style_panel>`{.interpreted-text
role="ref"}

The style of **PanelContainer**\'s background.
