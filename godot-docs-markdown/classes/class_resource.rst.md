github_url

:   hide

# Resource {#class_Resource}

**Inherits:** `RefCounted<class_RefCounted>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

**Inherited By:** `Animation<class_Animation>`{.interpreted-text
role="ref"},
`AnimationLibrary<class_AnimationLibrary>`{.interpreted-text
role="ref"}, `AnimationNode<class_AnimationNode>`{.interpreted-text
role="ref"},
`AnimationNodeStateMachinePlayback<class_AnimationNodeStateMachinePlayback>`{.interpreted-text
role="ref"},
`AnimationNodeStateMachineTransition<class_AnimationNodeStateMachineTransition>`{.interpreted-text
role="ref"}, `AudioBusLayout<class_AudioBusLayout>`{.interpreted-text
role="ref"}, `AudioEffect<class_AudioEffect>`{.interpreted-text
role="ref"}, `AudioStream<class_AudioStream>`{.interpreted-text
role="ref"}, `BitMap<class_BitMap>`{.interpreted-text role="ref"},
`BoneMap<class_BoneMap>`{.interpreted-text role="ref"},
`ButtonGroup<class_ButtonGroup>`{.interpreted-text role="ref"},
`CameraAttributes<class_CameraAttributes>`{.interpreted-text
role="ref"}, `ColorPalette<class_ColorPalette>`{.interpreted-text
role="ref"}, `Compositor<class_Compositor>`{.interpreted-text
role="ref"},
`CompositorEffect<class_CompositorEffect>`{.interpreted-text
role="ref"}, `CryptoKey<class_CryptoKey>`{.interpreted-text role="ref"},
`Curve<class_Curve>`{.interpreted-text role="ref"},
`Curve2D<class_Curve2D>`{.interpreted-text role="ref"},
`Curve3D<class_Curve3D>`{.interpreted-text role="ref"},
`EditorNode3DGizmoPlugin<class_EditorNode3DGizmoPlugin>`{.interpreted-text
role="ref"}, `EditorSettings<class_EditorSettings>`{.interpreted-text
role="ref"}, `Environment<class_Environment>`{.interpreted-text
role="ref"}, `Font<class_Font>`{.interpreted-text role="ref"},
`GDExtension<class_GDExtension>`{.interpreted-text role="ref"},
`GLTFAccessor<class_GLTFAccessor>`{.interpreted-text role="ref"},
`GLTFAnimation<class_GLTFAnimation>`{.interpreted-text role="ref"},
`GLTFBufferView<class_GLTFBufferView>`{.interpreted-text role="ref"},
`GLTFCamera<class_GLTFCamera>`{.interpreted-text role="ref"},
`GLTFDocument<class_GLTFDocument>`{.interpreted-text role="ref"},
`GLTFDocumentExtension<class_GLTFDocumentExtension>`{.interpreted-text
role="ref"}, `GLTFLight<class_GLTFLight>`{.interpreted-text role="ref"},
`GLTFMesh<class_GLTFMesh>`{.interpreted-text role="ref"},
`GLTFNode<class_GLTFNode>`{.interpreted-text role="ref"},
`GLTFPhysicsBody<class_GLTFPhysicsBody>`{.interpreted-text role="ref"},
`GLTFPhysicsShape<class_GLTFPhysicsShape>`{.interpreted-text
role="ref"}, `GLTFSkeleton<class_GLTFSkeleton>`{.interpreted-text
role="ref"}, `GLTFSkin<class_GLTFSkin>`{.interpreted-text role="ref"},
`GLTFSpecGloss<class_GLTFSpecGloss>`{.interpreted-text role="ref"},
`GLTFState<class_GLTFState>`{.interpreted-text role="ref"},
`GLTFTexture<class_GLTFTexture>`{.interpreted-text role="ref"},
`GLTFTextureSampler<class_GLTFTextureSampler>`{.interpreted-text
role="ref"}, `Gradient<class_Gradient>`{.interpreted-text role="ref"},
`Image<class_Image>`{.interpreted-text role="ref"},
`ImporterMesh<class_ImporterMesh>`{.interpreted-text role="ref"},
`InputEvent<class_InputEvent>`{.interpreted-text role="ref"},
`JSON<class_JSON>`{.interpreted-text role="ref"},
`LabelSettings<class_LabelSettings>`{.interpreted-text role="ref"},
`LightmapGIData<class_LightmapGIData>`{.interpreted-text role="ref"},
`Material<class_Material>`{.interpreted-text role="ref"},
`Mesh<class_Mesh>`{.interpreted-text role="ref"},
`MeshLibrary<class_MeshLibrary>`{.interpreted-text role="ref"},
`MissingResource<class_MissingResource>`{.interpreted-text role="ref"},
`MultiMesh<class_MultiMesh>`{.interpreted-text role="ref"},
`NavigationMesh<class_NavigationMesh>`{.interpreted-text role="ref"},
`NavigationMeshSourceGeometryData2D<class_NavigationMeshSourceGeometryData2D>`{.interpreted-text
role="ref"},
`NavigationMeshSourceGeometryData3D<class_NavigationMeshSourceGeometryData3D>`{.interpreted-text
role="ref"},
`NavigationPolygon<class_NavigationPolygon>`{.interpreted-text
role="ref"}, `Noise<class_Noise>`{.interpreted-text role="ref"},
`Occluder3D<class_Occluder3D>`{.interpreted-text role="ref"},
`OccluderPolygon2D<class_OccluderPolygon2D>`{.interpreted-text
role="ref"},
`OggPacketSequence<class_OggPacketSequence>`{.interpreted-text
role="ref"}, `OpenXRAction<class_OpenXRAction>`{.interpreted-text
role="ref"}, `OpenXRActionMap<class_OpenXRActionMap>`{.interpreted-text
role="ref"}, `OpenXRActionSet<class_OpenXRActionSet>`{.interpreted-text
role="ref"},
`OpenXRBindingModifier<class_OpenXRBindingModifier>`{.interpreted-text
role="ref"},
`OpenXRHapticBase<class_OpenXRHapticBase>`{.interpreted-text
role="ref"},
`OpenXRInteractionProfile<class_OpenXRInteractionProfile>`{.interpreted-text
role="ref"}, `OpenXRIPBinding<class_OpenXRIPBinding>`{.interpreted-text
role="ref"},
`PackedDataContainer<class_PackedDataContainer>`{.interpreted-text
role="ref"}, `PackedScene<class_PackedScene>`{.interpreted-text
role="ref"}, `PhysicsMaterial<class_PhysicsMaterial>`{.interpreted-text
role="ref"},
`PolygonPathFinder<class_PolygonPathFinder>`{.interpreted-text
role="ref"}, `RDShaderFile<class_RDShaderFile>`{.interpreted-text
role="ref"}, `RDShaderSPIRV<class_RDShaderSPIRV>`{.interpreted-text
role="ref"}, `RichTextEffect<class_RichTextEffect>`{.interpreted-text
role="ref"},
`SceneReplicationConfig<class_SceneReplicationConfig>`{.interpreted-text
role="ref"}, `Script<class_Script>`{.interpreted-text role="ref"},
`Shader<class_Shader>`{.interpreted-text role="ref"},
`ShaderInclude<class_ShaderInclude>`{.interpreted-text role="ref"},
`Shape2D<class_Shape2D>`{.interpreted-text role="ref"},
`Shape3D<class_Shape3D>`{.interpreted-text role="ref"},
`Shortcut<class_Shortcut>`{.interpreted-text role="ref"},
`SkeletonModification2D<class_SkeletonModification2D>`{.interpreted-text
role="ref"},
`SkeletonModificationStack2D<class_SkeletonModificationStack2D>`{.interpreted-text
role="ref"}, `SkeletonProfile<class_SkeletonProfile>`{.interpreted-text
role="ref"}, `Skin<class_Skin>`{.interpreted-text role="ref"},
`Sky<class_Sky>`{.interpreted-text role="ref"},
`SpriteFrames<class_SpriteFrames>`{.interpreted-text role="ref"},
`StyleBox<class_StyleBox>`{.interpreted-text role="ref"},
`SyntaxHighlighter<class_SyntaxHighlighter>`{.interpreted-text
role="ref"}, `Texture<class_Texture>`{.interpreted-text role="ref"},
`Theme<class_Theme>`{.interpreted-text role="ref"},
`TileMapPattern<class_TileMapPattern>`{.interpreted-text role="ref"},
`TileSet<class_TileSet>`{.interpreted-text role="ref"},
`TileSetSource<class_TileSetSource>`{.interpreted-text role="ref"},
`Translation<class_Translation>`{.interpreted-text role="ref"},
`VideoStream<class_VideoStream>`{.interpreted-text role="ref"},
`VideoStreamPlayback<class_VideoStreamPlayback>`{.interpreted-text
role="ref"},
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text
role="ref"}, `VoxelGIData<class_VoxelGIData>`{.interpreted-text
role="ref"}, `World2D<class_World2D>`{.interpreted-text role="ref"},
`World3D<class_World3D>`{.interpreted-text role="ref"},
`X509Certificate<class_X509Certificate>`{.interpreted-text role="ref"}

Base class for serializable objects.

::: rst-class
classref-introduction-group
:::

## Description

Resource is the base class for all Godot-specific resource types,
serving primarily as data containers. Since they inherit from
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"}, resources
are reference-counted and freed when no longer in use. They can also be
nested within other resources, and saved on disk.
`PackedScene<class_PackedScene>`{.interpreted-text role="ref"}, one of
the most common `Object<class_Object>`{.interpreted-text role="ref"}s in
a Godot project, is also a resource, uniquely capable of storing and
instantiating the `Node<class_Node>`{.interpreted-text role="ref"}s it
contains as many times as desired.

In GDScript, resources can loaded from disk by their
`resource_path<class_Resource_property_resource_path>`{.interpreted-text
role="ref"} using
`@GDScript.load()<class_@GDScript_method_load>`{.interpreted-text
role="ref"} or
`@GDScript.preload()<class_@GDScript_method_preload>`{.interpreted-text
role="ref"}.

The engine keeps a global cache of all loaded resources, referenced by
paths (see
`ResourceLoader.has_cached()<class_ResourceLoader_method_has_cached>`{.interpreted-text
role="ref"}). A resource will be cached when loaded for the first time
and removed from cache once all references are released. When a resource
is cached, subsequent loads using its path will return the cached
reference.

**Note:** In C#, resources will not be freed instantly after they are no
longer in use. Instead, garbage collection will run periodically and
will free resources that are no longer in use. This means that unused
resources will remain in memory for a while before being removed.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `Resources <../tutorials/scripting/resources>`{.interpreted-text
    role="doc"}
-   `When and how to avoid using nodes for everything <../tutorials/best_practices/node_alternatives>`{.interpreted-text
    role="doc"}

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-reftable-group
:::

## Methods

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Signals

::: {#class_Resource_signal_changed}
::: rst-class
classref-signal
:::
:::

**changed**() `🔗<class_Resource_signal_changed>`{.interpreted-text
role="ref"}

Emitted when the resource changes, usually when one of its properties is
modified. See also
`emit_changed()<class_Resource_method_emit_changed>`{.interpreted-text
role="ref"}.

**Note:** This signal is not emitted automatically for properties of
custom resources. If necessary, a setter needs to be created to emit the
signal.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_signal_setup_local_to_scene_requested}
::: rst-class
classref-signal
:::
:::

**setup_local_to_scene_requested**()
`🔗<class_Resource_signal_setup_local_to_scene_requested>`{.interpreted-text
role="ref"}

**Deprecated:** This signal is only emitted when the resource is
created. Override
`_setup_local_to_scene()<class_Resource_private_method__setup_local_to_scene>`{.interpreted-text
role="ref"} instead.

Emitted by a newly duplicated resource with
`resource_local_to_scene<class_Resource_property_resource_local_to_scene>`{.interpreted-text
role="ref"} set to `true`.

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Property Descriptions

::: {#class_Resource_property_resource_local_to_scene}
::: rst-class
classref-property
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"}
**resource_local_to_scene** = `false`
`🔗<class_Resource_property_resource_local_to_scene>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_local_to_scene**(value: `bool<class_bool>`{.interpreted-text
    role="ref"})
-   `bool<class_bool>`{.interpreted-text role="ref"}
    **is_local_to_scene**()

If `true`, the resource is duplicated for each instance of all scenes
using it. At run-time, the resource can be modified in one scene without
affecting other instances (see
`PackedScene.instantiate()<class_PackedScene_method_instantiate>`{.interpreted-text
role="ref"}).

**Note:** Changing this property at run-time has no effect on already
created duplicate resources.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_property_resource_name}
::: rst-class
classref-property
:::
:::

`String<class_String>`{.interpreted-text role="ref"} **resource_name** =
`""` `🔗<class_Resource_property_resource_name>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_name**(value: `String<class_String>`{.interpreted-text
    role="ref"})
-   `String<class_String>`{.interpreted-text role="ref"} **get_name**()

An optional name for this resource. When defined, its value is displayed
to represent the resource in the Inspector dock. For built-in scripts,
the name is displayed as part of the tab name in the script editor.

**Note:** Some resource formats do not support resource names. You can
still set the name in the editor or via code, but it will be lost when
the resource is reloaded. For example, only built-in scripts can have a
resource name, while scripts stored in separate files cannot.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_property_resource_path}
::: rst-class
classref-property
:::
:::

`String<class_String>`{.interpreted-text role="ref"} **resource_path** =
`""` `🔗<class_Resource_property_resource_path>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_path**(value: `String<class_String>`{.interpreted-text
    role="ref"})
-   `String<class_String>`{.interpreted-text role="ref"} **get_path**()

The unique path to this resource. If it has been saved to disk, the
value will be its filepath. If the resource is exclusively contained
within a scene, the value will be the
`PackedScene<class_PackedScene>`{.interpreted-text role="ref"}\'s
filepath, followed by a unique identifier.

**Note:** Setting this property manually may fail if a resource with the
same path has already been previously loaded. If necessary, use
`take_over_path()<class_Resource_method_take_over_path>`{.interpreted-text
role="ref"}.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_property_resource_scene_unique_id}
::: rst-class
classref-property
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**resource_scene_unique_id**
`🔗<class_Resource_property_resource_scene_unique_id>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_scene_unique_id**(value:
    `String<class_String>`{.interpreted-text role="ref"})
-   `String<class_String>`{.interpreted-text role="ref"}
    **get_scene_unique_id**()

An unique identifier relative to the this resource\'s scene. If left
empty, the ID is automatically generated when this resource is saved
inside a `PackedScene<class_PackedScene>`{.interpreted-text role="ref"}.
If the resource is not inside a scene, this property is empty by
default.

**Note:** When the `PackedScene<class_PackedScene>`{.interpreted-text
role="ref"} is saved, if multiple resources in the same scene use the
same ID, only the earliest resource in the scene hierarchy keeps the
original ID. The other resources are assigned new IDs from
`generate_scene_unique_id()<class_Resource_method_generate_scene_unique_id>`{.interpreted-text
role="ref"}.

**Note:** Setting this property does not emit the
`changed<class_Resource_signal_changed>`{.interpreted-text role="ref"}
signal.

**Warning:** When setting, the ID must only consist of letters, numbers,
and underscores. Otherwise, it will fail and default to a randomly
generated ID.

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Method Descriptions

::: {#class_Resource_private_method__get_rid}
::: rst-class
classref-method
:::
:::

`RID<class_RID>`{.interpreted-text role="ref"} **\_get_rid**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_Resource_private_method__get_rid>`{.interpreted-text
role="ref"}

Override this method to return a custom
`RID<class_RID>`{.interpreted-text role="ref"} when
`get_rid()<class_Resource_method_get_rid>`{.interpreted-text role="ref"}
is called.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_private_method__reset_state}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_reset_state**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_Resource_private_method__reset_state>`{.interpreted-text
role="ref"}

For resources that use a variable number of properties, either via
`Object._validate_property()<class_Object_private_method__validate_property>`{.interpreted-text
role="ref"} or
`Object._get_property_list()<class_Object_private_method__get_property_list>`{.interpreted-text
role="ref"}, this method should be implemented to correctly clear the
resource\'s state.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_private_method__set_path_cache}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_set_path_cache**(path: `String<class_String>`{.interpreted-text
role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_Resource_private_method__set_path_cache>`{.interpreted-text
role="ref"}

Sets the resource\'s path to `path` without involving the resource
cache.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_private_method__setup_local_to_scene}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**\_setup_local_to_scene**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_Resource_private_method__setup_local_to_scene>`{.interpreted-text
role="ref"}

Override this method to customize the newly duplicated resource created
from
`PackedScene.instantiate()<class_PackedScene_method_instantiate>`{.interpreted-text
role="ref"}, if the original\'s
`resource_local_to_scene<class_Resource_property_resource_local_to_scene>`{.interpreted-text
role="ref"} is set to `true`.

**Example:** Set a random `damage` value to every local resource from an
instantiated scene:

    extends Resource

    var damage = 0

    func _setup_local_to_scene():
        damage = randi_range(10, 40)

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_duplicate}
::: rst-class
classref-method
:::
:::

`Resource<class_Resource>`{.interpreted-text role="ref"}
**duplicate**(subresources: `bool<class_bool>`{.interpreted-text
role="ref"} = false)
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"} `🔗<class_Resource_method_duplicate>`{.interpreted-text
role="ref"}

Duplicates this resource, returning a new resource with its `export`ed
or
`@GlobalScope.PROPERTY_USAGE_STORAGE<class_@GlobalScope_constant_PROPERTY_USAGE_STORAGE>`{.interpreted-text
role="ref"} properties copied from the original.

If `subresources` is `false`, a shallow copy is returned; nested
resources within subresources are not duplicated and are shared with the
original resource (with one exception; see below). If `subresources` is
`true`, a deep copy is returned; nested subresources will be duplicated
and are not shared (with two exceptions; see below).

`subresources` is usually respected, with the following exceptions:

-   Subresource properties with the
    `@GlobalScope.PROPERTY_USAGE_ALWAYS_DUPLICATE<class_@GlobalScope_constant_PROPERTY_USAGE_ALWAYS_DUPLICATE>`{.interpreted-text
    role="ref"} flag are always duplicated.
-   Subresource properties with the
    `@GlobalScope.PROPERTY_USAGE_NEVER_DUPLICATE<class_@GlobalScope_constant_PROPERTY_USAGE_NEVER_DUPLICATE>`{.interpreted-text
    role="ref"} flag are never duplicated.
-   Subresources inside `Array<class_Array>`{.interpreted-text
    role="ref"} and `Dictionary<class_Dictionary>`{.interpreted-text
    role="ref"} properties are never duplicated.

**Note:** For custom resources, this method will fail if
`Object._init()<class_Object_private_method__init>`{.interpreted-text
role="ref"} has been defined with required parameters.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_emit_changed}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**emit_changed**()
`🔗<class_Resource_method_emit_changed>`{.interpreted-text role="ref"}

Emits the `changed<class_Resource_signal_changed>`{.interpreted-text
role="ref"} signal. This method is called automatically for some
built-in resources.

**Note:** For custom resources, it\'s recommended to call this method
whenever a meaningful change occurs, such as a modified property. This
ensures that custom `Object<class_Object>`{.interpreted-text
role="ref"}s depending on the resource are properly updated.

    var damage:
        set(new_value):
            if damage != new_value:
                damage = new_value
                emit_changed()

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_generate_scene_unique_id}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**generate_scene_unique_id**()
`static (This method doesn't need an instance to be called, so it can be called directly using the class name.)`{.interpreted-text
role="abbr"}
`🔗<class_Resource_method_generate_scene_unique_id>`{.interpreted-text
role="ref"}

Generates a unique identifier for a resource to be contained inside a
`PackedScene<class_PackedScene>`{.interpreted-text role="ref"}, based on
the current date, time, and a random value. The returned string is only
composed of letters (`a` to `y`) and numbers (`0` to `8`). See also
`resource_scene_unique_id<class_Resource_property_resource_scene_unique_id>`{.interpreted-text
role="ref"}.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_get_id_for_path}
::: rst-class
classref-method
:::
:::

`String<class_String>`{.interpreted-text role="ref"}
**get_id_for_path**(path: `String<class_String>`{.interpreted-text
role="ref"})
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_Resource_method_get_id_for_path>`{.interpreted-text
role="ref"}

Returns the unique identifier for the resource with the given `path`
from the resource cache. If the resource is not loaded and cached, an
empty string is returned.

**Note:** This method is only implemented when running in an editor
context. At runtime, it returns an empty string.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_get_local_scene}
::: rst-class
classref-method
:::
:::

`Node<class_Node>`{.interpreted-text role="ref"} **get_local_scene**()
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_Resource_method_get_local_scene>`{.interpreted-text
role="ref"}

If
`resource_local_to_scene<class_Resource_property_resource_local_to_scene>`{.interpreted-text
role="ref"} is set to `true` and the resource has been loaded from a
`PackedScene<class_PackedScene>`{.interpreted-text role="ref"}
instantiation, returns the root `Node<class_Node>`{.interpreted-text
role="ref"} of the scene where this resource is used. Otherwise, returns
`null`.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_get_rid}
::: rst-class
classref-method
:::
:::

`RID<class_RID>`{.interpreted-text role="ref"} **get_rid**()
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"} `🔗<class_Resource_method_get_rid>`{.interpreted-text
role="ref"}

Returns the `RID<class_RID>`{.interpreted-text role="ref"} of this
resource (or an empty RID). Many resources (such as
`Texture2D<class_Texture2D>`{.interpreted-text role="ref"},
`Mesh<class_Mesh>`{.interpreted-text role="ref"}, and so on) are
high-level abstractions of resources stored in a specialized server
(`DisplayServer<class_DisplayServer>`{.interpreted-text role="ref"},
`RenderingServer<class_RenderingServer>`{.interpreted-text role="ref"},
etc.), so this function will return the original
`RID<class_RID>`{.interpreted-text role="ref"}.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_is_built_in}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"} **is_built_in**()
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"} `🔗<class_Resource_method_is_built_in>`{.interpreted-text
role="ref"}

Returns `true` if the resource is built-in (from the engine) or `false`
if it is user-defined.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_reset_state}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**reset_state**()
`🔗<class_Resource_method_reset_state>`{.interpreted-text role="ref"}

For resources that use a variable number of properties, either via
`Object._validate_property()<class_Object_private_method__validate_property>`{.interpreted-text
role="ref"} or
`Object._get_property_list()<class_Object_private_method__get_property_list>`{.interpreted-text
role="ref"}, override
`_reset_state()<class_Resource_private_method__reset_state>`{.interpreted-text
role="ref"} to correctly clear the resource\'s state.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_set_id_for_path}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**set_id_for_path**(path: `String<class_String>`{.interpreted-text
role="ref"}, id: `String<class_String>`{.interpreted-text role="ref"})
`🔗<class_Resource_method_set_id_for_path>`{.interpreted-text
role="ref"}

Sets the unique identifier to `id` for the resource with the given
`path` in the resource cache. If the unique identifier is empty, the
cache entry using `path` is removed if it exists.

**Note:** This method is only implemented when running in an editor
context.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_set_path_cache}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**set_path_cache**(path: `String<class_String>`{.interpreted-text
role="ref"})
`🔗<class_Resource_method_set_path_cache>`{.interpreted-text role="ref"}

Sets the resource\'s path to `path` without involving the resource
cache.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_setup_local_to_scene}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**setup_local_to_scene**()
`🔗<class_Resource_method_setup_local_to_scene>`{.interpreted-text
role="ref"}

**Deprecated:** This method should only be called internally.

Calls
`_setup_local_to_scene()<class_Resource_private_method__setup_local_to_scene>`{.interpreted-text
role="ref"}. If
`resource_local_to_scene<class_Resource_property_resource_local_to_scene>`{.interpreted-text
role="ref"} is set to `true`, this method is automatically called from
`PackedScene.instantiate()<class_PackedScene_method_instantiate>`{.interpreted-text
role="ref"} by the newly duplicated resource within the scene instance.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_Resource_method_take_over_path}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**take_over_path**(path: `String<class_String>`{.interpreted-text
role="ref"})
`🔗<class_Resource_method_take_over_path>`{.interpreted-text role="ref"}

Sets the
`resource_path<class_Resource_property_resource_path>`{.interpreted-text
role="ref"} to `path`, potentially overriding an existing cache entry
for this path. Further attempts to load an overridden resource by path
will instead return this resource.
