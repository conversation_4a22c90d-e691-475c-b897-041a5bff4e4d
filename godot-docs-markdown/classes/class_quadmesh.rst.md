github_url

:   hide

# QuadMesh {#class_QuadMesh}

**Inherits:** `PlaneMesh<class_PlaneMesh>`{.interpreted-text role="ref"}
**\<** `PrimitiveMesh<class_PrimitiveMesh>`{.interpreted-text
role="ref"} **\<** `Mesh<class_Mesh>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Class representing a square mesh facing the camera.

::: rst-class
classref-introduction-group
:::

## Description

Class representing a square
`PrimitiveMesh<class_PrimitiveMesh>`{.interpreted-text role="ref"}. This
flat mesh does not have a thickness. By default, this mesh is aligned on
the X and Y axes; this rotation is more suited for use with billboarded
materials. A **QuadMesh** is equivalent to a
`PlaneMesh<class_PlaneMesh>`{.interpreted-text role="ref"} except its
default
`PlaneMesh.orientation<class_PlaneMesh_property_orientation>`{.interpreted-text
role="ref"} is
`PlaneMesh.FACE_Z<class_PlaneMesh_constant_FACE_Z>`{.interpreted-text
role="ref"}.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   [GUI in 3D Viewport
    Demo](https://godotengine.org/asset-library/asset/2807)
-   [2D in 3D Viewport
    Demo](https://godotengine.org/asset-library/asset/2803)

::: rst-class
classref-reftable-group
:::

## Properties

