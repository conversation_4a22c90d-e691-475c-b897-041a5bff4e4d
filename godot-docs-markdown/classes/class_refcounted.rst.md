github_url

:   hide

# RefCounted {#class_RefCounted}

**Inherits:** `Object<class_Object>`{.interpreted-text role="ref"}

**Inherited By:** `AESContext<class_AESContext>`{.interpreted-text
role="ref"}, `AStar2D<class_AStar2D>`{.interpreted-text role="ref"},
`AStar3D<class_AStar3D>`{.interpreted-text role="ref"},
`AStarGrid2D<class_AStarGrid2D>`{.interpreted-text role="ref"},
`AudioEffectInstance<class_AudioEffectInstance>`{.interpreted-text
role="ref"}, `AudioSample<class_AudioSample>`{.interpreted-text
role="ref"},
`AudioSamplePlayback<class_AudioSamplePlayback>`{.interpreted-text
role="ref"},
`AudioStreamPlayback<class_AudioStreamPlayback>`{.interpreted-text
role="ref"}, `CameraFeed<class_CameraFeed>`{.interpreted-text
role="ref"}, `CharFXTransform<class_CharFXTransform>`{.interpreted-text
role="ref"}, `ConfigFile<class_ConfigFile>`{.interpreted-text
role="ref"}, `Crypto<class_Crypto>`{.interpreted-text role="ref"},
`DirAccess<class_DirAccess>`{.interpreted-text role="ref"},
`DTLSServer<class_DTLSServer>`{.interpreted-text role="ref"},
`EditorContextMenuPlugin<class_EditorContextMenuPlugin>`{.interpreted-text
role="ref"},
`EditorDebuggerPlugin<class_EditorDebuggerPlugin>`{.interpreted-text
role="ref"},
`EditorDebuggerSession<class_EditorDebuggerSession>`{.interpreted-text
role="ref"},
`EditorExportPlatform<class_EditorExportPlatform>`{.interpreted-text
role="ref"},
`EditorExportPlugin<class_EditorExportPlugin>`{.interpreted-text
role="ref"},
`EditorExportPreset<class_EditorExportPreset>`{.interpreted-text
role="ref"},
`EditorFeatureProfile<class_EditorFeatureProfile>`{.interpreted-text
role="ref"},
`EditorFileSystemImportFormatSupportQuery<class_EditorFileSystemImportFormatSupportQuery>`{.interpreted-text
role="ref"},
`EditorInspectorPlugin<class_EditorInspectorPlugin>`{.interpreted-text
role="ref"},
`EditorResourceConversionPlugin<class_EditorResourceConversionPlugin>`{.interpreted-text
role="ref"},
`EditorResourcePreviewGenerator<class_EditorResourcePreviewGenerator>`{.interpreted-text
role="ref"},
`EditorResourceTooltipPlugin<class_EditorResourceTooltipPlugin>`{.interpreted-text
role="ref"},
`EditorSceneFormatImporter<class_EditorSceneFormatImporter>`{.interpreted-text
role="ref"},
`EditorScenePostImport<class_EditorScenePostImport>`{.interpreted-text
role="ref"},
`EditorScenePostImportPlugin<class_EditorScenePostImportPlugin>`{.interpreted-text
role="ref"}, `EditorScript<class_EditorScript>`{.interpreted-text
role="ref"},
`EditorTranslationParserPlugin<class_EditorTranslationParserPlugin>`{.interpreted-text
role="ref"},
`EncodedObjectAsID<class_EncodedObjectAsID>`{.interpreted-text
role="ref"}, `ENetConnection<class_ENetConnection>`{.interpreted-text
role="ref"}, `EngineProfiler<class_EngineProfiler>`{.interpreted-text
role="ref"}, `Expression<class_Expression>`{.interpreted-text
role="ref"}, `FileAccess<class_FileAccess>`{.interpreted-text
role="ref"},
`GLTFObjectModelProperty<class_GLTFObjectModelProperty>`{.interpreted-text
role="ref"}, `HashingContext<class_HashingContext>`{.interpreted-text
role="ref"}, `HMACContext<class_HMACContext>`{.interpreted-text
role="ref"}, `HTTPClient<class_HTTPClient>`{.interpreted-text
role="ref"},
`ImageFormatLoader<class_ImageFormatLoader>`{.interpreted-text
role="ref"}, `JavaClass<class_JavaClass>`{.interpreted-text role="ref"},
`JavaObject<class_JavaObject>`{.interpreted-text role="ref"},
`JavaScriptObject<class_JavaScriptObject>`{.interpreted-text
role="ref"},
`KinematicCollision2D<class_KinematicCollision2D>`{.interpreted-text
role="ref"},
`KinematicCollision3D<class_KinematicCollision3D>`{.interpreted-text
role="ref"}, `Lightmapper<class_Lightmapper>`{.interpreted-text
role="ref"},
`MeshConvexDecompositionSettings<class_MeshConvexDecompositionSettings>`{.interpreted-text
role="ref"}, `MeshDataTool<class_MeshDataTool>`{.interpreted-text
role="ref"}, `MultiplayerAPI<class_MultiplayerAPI>`{.interpreted-text
role="ref"}, `Mutex<class_Mutex>`{.interpreted-text role="ref"},
`NavigationPathQueryParameters2D<class_NavigationPathQueryParameters2D>`{.interpreted-text
role="ref"},
`NavigationPathQueryParameters3D<class_NavigationPathQueryParameters3D>`{.interpreted-text
role="ref"},
`NavigationPathQueryResult2D<class_NavigationPathQueryResult2D>`{.interpreted-text
role="ref"},
`NavigationPathQueryResult3D<class_NavigationPathQueryResult3D>`{.interpreted-text
role="ref"}, `Node3DGizmo<class_Node3DGizmo>`{.interpreted-text
role="ref"},
`OggPacketSequencePlayback<class_OggPacketSequencePlayback>`{.interpreted-text
role="ref"},
`OpenXRAPIExtension<class_OpenXRAPIExtension>`{.interpreted-text
role="ref"},
`PackedDataContainerRef<class_PackedDataContainerRef>`{.interpreted-text
role="ref"}, `PacketPeer<class_PacketPeer>`{.interpreted-text
role="ref"}, `PCKPacker<class_PCKPacker>`{.interpreted-text role="ref"},
`PhysicsPointQueryParameters2D<class_PhysicsPointQueryParameters2D>`{.interpreted-text
role="ref"},
`PhysicsPointQueryParameters3D<class_PhysicsPointQueryParameters3D>`{.interpreted-text
role="ref"},
`PhysicsRayQueryParameters2D<class_PhysicsRayQueryParameters2D>`{.interpreted-text
role="ref"},
`PhysicsRayQueryParameters3D<class_PhysicsRayQueryParameters3D>`{.interpreted-text
role="ref"},
`PhysicsShapeQueryParameters2D<class_PhysicsShapeQueryParameters2D>`{.interpreted-text
role="ref"},
`PhysicsShapeQueryParameters3D<class_PhysicsShapeQueryParameters3D>`{.interpreted-text
role="ref"},
`PhysicsTestMotionParameters2D<class_PhysicsTestMotionParameters2D>`{.interpreted-text
role="ref"},
`PhysicsTestMotionParameters3D<class_PhysicsTestMotionParameters3D>`{.interpreted-text
role="ref"},
`PhysicsTestMotionResult2D<class_PhysicsTestMotionResult2D>`{.interpreted-text
role="ref"},
`PhysicsTestMotionResult3D<class_PhysicsTestMotionResult3D>`{.interpreted-text
role="ref"},
`RandomNumberGenerator<class_RandomNumberGenerator>`{.interpreted-text
role="ref"},
`RDAttachmentFormat<class_RDAttachmentFormat>`{.interpreted-text
role="ref"},
`RDFramebufferPass<class_RDFramebufferPass>`{.interpreted-text
role="ref"},
`RDPipelineColorBlendState<class_RDPipelineColorBlendState>`{.interpreted-text
role="ref"},
`RDPipelineColorBlendStateAttachment<class_RDPipelineColorBlendStateAttachment>`{.interpreted-text
role="ref"},
`RDPipelineDepthStencilState<class_RDPipelineDepthStencilState>`{.interpreted-text
role="ref"},
`RDPipelineMultisampleState<class_RDPipelineMultisampleState>`{.interpreted-text
role="ref"},
`RDPipelineRasterizationState<class_RDPipelineRasterizationState>`{.interpreted-text
role="ref"},
`RDPipelineSpecializationConstant<class_RDPipelineSpecializationConstant>`{.interpreted-text
role="ref"}, `RDSamplerState<class_RDSamplerState>`{.interpreted-text
role="ref"}, `RDShaderSource<class_RDShaderSource>`{.interpreted-text
role="ref"}, `RDTextureFormat<class_RDTextureFormat>`{.interpreted-text
role="ref"}, `RDTextureView<class_RDTextureView>`{.interpreted-text
role="ref"}, `RDUniform<class_RDUniform>`{.interpreted-text role="ref"},
`RDVertexAttribute<class_RDVertexAttribute>`{.interpreted-text
role="ref"}, `RegEx<class_RegEx>`{.interpreted-text role="ref"},
`RegExMatch<class_RegExMatch>`{.interpreted-text role="ref"},
`RenderSceneBuffers<class_RenderSceneBuffers>`{.interpreted-text
role="ref"},
`RenderSceneBuffersConfiguration<class_RenderSceneBuffersConfiguration>`{.interpreted-text
role="ref"}, `Resource<class_Resource>`{.interpreted-text role="ref"},
`ResourceFormatLoader<class_ResourceFormatLoader>`{.interpreted-text
role="ref"},
`ResourceFormatSaver<class_ResourceFormatSaver>`{.interpreted-text
role="ref"},
`ResourceImporter<class_ResourceImporter>`{.interpreted-text
role="ref"}, `SceneState<class_SceneState>`{.interpreted-text
role="ref"}, `SceneTreeTimer<class_SceneTreeTimer>`{.interpreted-text
role="ref"}, `Semaphore<class_Semaphore>`{.interpreted-text role="ref"},
`SkinReference<class_SkinReference>`{.interpreted-text role="ref"},
`StreamPeer<class_StreamPeer>`{.interpreted-text role="ref"},
`SurfaceTool<class_SurfaceTool>`{.interpreted-text role="ref"},
`TCPServer<class_TCPServer>`{.interpreted-text role="ref"},
`TextLine<class_TextLine>`{.interpreted-text role="ref"},
`TextParagraph<class_TextParagraph>`{.interpreted-text role="ref"},
`TextServer<class_TextServer>`{.interpreted-text role="ref"},
`Thread<class_Thread>`{.interpreted-text role="ref"},
`TLSOptions<class_TLSOptions>`{.interpreted-text role="ref"},
`TranslationDomain<class_TranslationDomain>`{.interpreted-text
role="ref"}, `TriangleMesh<class_TriangleMesh>`{.interpreted-text
role="ref"}, `Tween<class_Tween>`{.interpreted-text role="ref"},
`Tweener<class_Tweener>`{.interpreted-text role="ref"},
`UDPServer<class_UDPServer>`{.interpreted-text role="ref"},
`UPNP<class_UPNP>`{.interpreted-text role="ref"},
`UPNPDevice<class_UPNPDevice>`{.interpreted-text role="ref"},
`WeakRef<class_WeakRef>`{.interpreted-text role="ref"},
`WebRTCPeerConnection<class_WebRTCPeerConnection>`{.interpreted-text
role="ref"}, `XMLParser<class_XMLParser>`{.interpreted-text role="ref"},
`XRInterface<class_XRInterface>`{.interpreted-text role="ref"},
`XRPose<class_XRPose>`{.interpreted-text role="ref"},
`XRTracker<class_XRTracker>`{.interpreted-text role="ref"},
`ZIPPacker<class_ZIPPacker>`{.interpreted-text role="ref"},
`ZIPReader<class_ZIPReader>`{.interpreted-text role="ref"}

Base class for reference-counted objects.

::: rst-class
classref-introduction-group
:::

## Description

Base class for any object that keeps a reference count.
`Resource<class_Resource>`{.interpreted-text role="ref"} and many other
helper objects inherit this class.

Unlike other `Object<class_Object>`{.interpreted-text role="ref"} types,
**RefCounted**s keep an internal reference counter so that they are
automatically released when no longer in use, and only then.
**RefCounted**s therefore do not need to be freed manually with
`Object.free()<class_Object_method_free>`{.interpreted-text role="ref"}.

**RefCounted** instances caught in a cyclic reference will **not** be
freed automatically. For example, if a node holds a reference to
instance `A`, which directly or indirectly holds a reference back to
`A`, `A`\'s reference count will be 2. Destruction of the node will
leave `A` dangling with a reference count of 1, and there will be a
memory leak. To prevent this, one of the references in the cycle can be
made weak with
`@GlobalScope.weakref()<class_@GlobalScope_method_weakref>`{.interpreted-text
role="ref"}.

In the vast majority of use cases, instantiating and using
**RefCounted**-derived types is all you need to do. The methods provided
in this class are only for advanced users, and can cause issues if
misused.

**Note:** In C#, reference-counted objects will not be freed instantly
after they are no longer in use. Instead, garbage collection will run
periodically and will free reference-counted objects that are no longer
in use. This means that unused ones will remain in memory for a while
before being removed.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `When and how to avoid using nodes for everything <../tutorials/best_practices/node_alternatives>`{.interpreted-text
    role="doc"}

::: rst-class
classref-reftable-group
:::

## Methods

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Method Descriptions

::: {#class_RefCounted_method_get_reference_count}
::: rst-class
classref-method
:::
:::

`int<class_int>`{.interpreted-text role="ref"} **get_reference_count**()
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_RefCounted_method_get_reference_count>`{.interpreted-text
role="ref"}

Returns the current reference count.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_RefCounted_method_init_ref}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"} **init_ref**()
`🔗<class_RefCounted_method_init_ref>`{.interpreted-text role="ref"}

Initializes the internal reference counter. Use this only if you really
know what you are doing.

Returns whether the initialization was successful.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_RefCounted_method_reference}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"} **reference**()
`🔗<class_RefCounted_method_reference>`{.interpreted-text role="ref"}

Increments the internal reference counter. Use this only if you really
know what you are doing.

Returns `true` if the increment was successful, `false` otherwise.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_RefCounted_method_unreference}
::: rst-class
classref-method
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"} **unreference**()
`🔗<class_RefCounted_method_unreference>`{.interpreted-text role="ref"}

Decrements the internal reference counter. Use this only if you really
know what you are doing.

Returns `true` if the object should be freed after the decrement,
`false` otherwise.
