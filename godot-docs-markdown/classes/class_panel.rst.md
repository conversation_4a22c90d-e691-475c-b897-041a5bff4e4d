github_url

:   hide

# Panel {#class_Panel}

**Inherits:** `Control<class_Control>`{.interpreted-text role="ref"}
**\<** `CanvasItem<class_CanvasItem>`{.interpreted-text role="ref"}
**\<** `Node<class_Node>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A GUI control that displays a
`StyleBox<class_StyleBox>`{.interpreted-text role="ref"}.

::: rst-class
classref-introduction-group
:::

## Description

**Panel** is a GUI control that displays a
`StyleBox<class_StyleBox>`{.interpreted-text role="ref"}. See also
`PanelContainer<class_PanelContainer>`{.interpreted-text role="ref"}.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   [2D Role Playing Game (RPG)
    Demo](https://godotengine.org/asset-library/asset/2729)
-   [Hierarchical Finite State Machine
    Demo](https://godotengine.org/asset-library/asset/2714)

::: rst-class
classref-reftable-group
:::

## Theme Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Theme Property Descriptions

::: {#class_Panel_theme_style_panel}
::: rst-class
classref-themeproperty
:::
:::

`StyleBox<class_StyleBox>`{.interpreted-text role="ref"} **panel**
`🔗<class_Panel_theme_style_panel>`{.interpreted-text role="ref"}

The `StyleBox<class_StyleBox>`{.interpreted-text role="ref"} of this
control.
