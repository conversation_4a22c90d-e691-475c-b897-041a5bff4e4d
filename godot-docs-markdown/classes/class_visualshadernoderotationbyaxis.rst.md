github_url

:   hide

# VisualShaderNodeRotationByAxis {#class_VisualShaderNodeRotationByAxis}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A visual shader node that modifies the rotation of the object using a
rotation matrix.

::: rst-class
classref-introduction-group
:::

## Description

RotationByAxis node will transform the vertices of a mesh with specified
axis and angle in radians. It can be used to rotate an object in an
arbitrary axis.
