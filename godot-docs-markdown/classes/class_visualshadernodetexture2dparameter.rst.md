github_url

:   hide

# VisualShaderNodeTexture2DParameter {#class_VisualShaderNodeTexture2DParameter}

**Inherits:**
`VisualShaderNodeTextureParameter<class_VisualShaderNodeTextureParameter>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNodeParameter<class_VisualShaderNodeParameter>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Provides a 2D texture parameter within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Translated to `uniform sampler2D` in the shader language.
