github_url

:   hide

# VisualShaderNodeOutput {#class_VisualShaderNodeOutput}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

**Inherited By:**
`VisualShaderNodeParticleOutput<class_VisualShaderNodeParticleOutput>`{.interpreted-text
role="ref"}

Represents the output shader parameters within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

This visual shader node is present in all shader graphs in form of
\"Output\" block with multiple output value ports.
