github_url

:   hide

# VisualShaderNodeDeterminant {#class_VisualShaderNodeDeterminant}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Calculates the determinant of a
`Transform3D<class_Transform3D>`{.interpreted-text role="ref"} within
the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Translates to `determinant(x)` in the shader language.
