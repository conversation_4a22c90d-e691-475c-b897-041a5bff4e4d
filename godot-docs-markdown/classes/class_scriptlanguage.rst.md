github_url

:   hide

# ScriptLanguage {#class_ScriptLanguage}

**Inherits:** `Object<class_Object>`{.interpreted-text role="ref"}

**Inherited By:**
`ScriptLanguageExtension<class_ScriptLanguageExtension>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this class. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Enumerations

::: {#enum_ScriptLanguage_ScriptNameCasing}
::: rst-class
classref-enumeration
:::
:::

enum **ScriptNameCasing**:
`🔗<enum_ScriptLanguage_ScriptNameCasing>`{.interpreted-text role="ref"}

::: {#class_ScriptLanguage_constant_SCRIPT_NAME_CASING_AUTO}
::: rst-class
classref-enumeration-constant
:::
:::

`ScriptNameCasing<enum_ScriptLanguage_ScriptNameCasing>`{.interpreted-text
role="ref"} **SCRIPT_NAME_CASING_AUTO** = `0`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguage_constant_SCRIPT_NAME_CASING_PASCAL_CASE}
::: rst-class
classref-enumeration-constant
:::
:::

`ScriptNameCasing<enum_ScriptLanguage_ScriptNameCasing>`{.interpreted-text
role="ref"} **SCRIPT_NAME_CASING_PASCAL_CASE** = `1`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguage_constant_SCRIPT_NAME_CASING_SNAKE_CASE}
::: rst-class
classref-enumeration-constant
:::
:::

`ScriptNameCasing<enum_ScriptLanguage_ScriptNameCasing>`{.interpreted-text
role="ref"} **SCRIPT_NAME_CASING_SNAKE_CASE** = `2`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: {#class_ScriptLanguage_constant_SCRIPT_NAME_CASING_KEBAB_CASE}
::: rst-class
classref-enumeration-constant
:::
:::

`ScriptNameCasing<enum_ScriptLanguage_ScriptNameCasing>`{.interpreted-text
role="ref"} **SCRIPT_NAME_CASING_KEBAB_CASE** = `3`

::: {.container .contribute}
There is currently no description for this enum. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::
