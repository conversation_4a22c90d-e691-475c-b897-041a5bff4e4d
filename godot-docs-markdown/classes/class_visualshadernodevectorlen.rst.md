github_url

:   hide

# VisualShaderNodeVectorLen {#class_VisualShaderNodeVectorLen}

**Inherits:**
`VisualShaderNodeVectorBase<class_VisualShaderNodeVectorBase>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Returns the length of a `Vector3<class_Vector3>`{.interpreted-text
role="ref"} within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Translated to `length(p0)` in the shader language.
