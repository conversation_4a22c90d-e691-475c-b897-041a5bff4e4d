github_url

:   hide

# VisualShaderNodeVec2Constant {#class_VisualShaderNodeVec2Constant}

**Inherits:**
`VisualShaderNodeConstant<class_VisualShaderNodeConstant>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A `Vector2<class_Vector2>`{.interpreted-text role="ref"} constant to be
used within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

A constant `Vector2<class_Vector2>`{.interpreted-text role="ref"}, which
can be used as an input node.

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Property Descriptions

::: {#class_VisualShaderNodeVec2Constant_property_constant}
::: rst-class
classref-property
:::
:::

`Vector2<class_Vector2>`{.interpreted-text role="ref"} **constant** =
`Vector2(0, 0)`
`🔗<class_VisualShaderNodeVec2Constant_property_constant>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_constant**(value: `Vector2<class_Vector2>`{.interpreted-text
    role="ref"})
-   `Vector2<class_Vector2>`{.interpreted-text role="ref"}
    **get_constant**()

A `Vector2<class_Vector2>`{.interpreted-text role="ref"} constant which
represents the state of this node.
