github_url

:   hide

# VisualShaderNodeCubemapParameter {#class_VisualShaderNodeCubemapParameter}

**Inherits:**
`VisualShaderNodeTextureParameter<class_VisualShaderNodeTextureParameter>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNodeParameter<class_VisualShaderNodeParameter>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A `Cubemap<class_Cubemap>`{.interpreted-text role="ref"} parameter node
to be used within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Translated to `uniform samplerCube` in the shader language. The output
value can be used as port for
`VisualShaderNodeCubemap<class_VisualShaderNodeCubemap>`{.interpreted-text
role="ref"}.
