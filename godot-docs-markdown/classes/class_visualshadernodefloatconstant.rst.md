github_url

:   hide

# VisualShaderNodeFloatConstant {#class_VisualShaderNodeFloatConstant}

**Inherits:**
`VisualShaderNodeConstant<class_VisualShaderNodeConstant>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A scalar floating-point constant to be used within the visual shader
graph.

::: rst-class
classref-introduction-group
:::

## Description

Translated to `float` in the shader language.

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Property Descriptions

::: {#class_VisualShaderNodeFloatConstant_property_constant}
::: rst-class
classref-property
:::
:::

`float<class_float>`{.interpreted-text role="ref"} **constant** = `0.0`
`🔗<class_VisualShaderNodeFloatConstant_property_constant>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_constant**(value: `float<class_float>`{.interpreted-text
    role="ref"})
-   `float<class_float>`{.interpreted-text role="ref"}
    **get_constant**()

A floating-point constant which represents a state of this node.
