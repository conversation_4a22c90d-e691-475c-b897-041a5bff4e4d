github_url

:   hide

# VFlowContainer {#class_VFlowContainer}

**Inherits:** `FlowContainer<class_FlowContainer>`{.interpreted-text
role="ref"} **\<** `Container<class_Container>`{.interpreted-text
role="ref"} **\<** `Control<class_Control>`{.interpreted-text
role="ref"} **\<** `CanvasItem<class_CanvasItem>`{.interpreted-text
role="ref"} **\<** `Node<class_Node>`{.interpreted-text role="ref"}
**\<** `Object<class_Object>`{.interpreted-text role="ref"}

A container that arranges its child controls vertically and wraps them
around at the borders.

::: rst-class
classref-introduction-group
:::

## Description

A variant of `FlowContainer<class_FlowContainer>`{.interpreted-text
role="ref"} that can only arrange its child controls vertically,
wrapping them around at the borders. This is similar to how text in a
book wraps around when no more words can fit on a line, except
vertically.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `Using Containers <../tutorials/ui/gui_containers>`{.interpreted-text
    role="doc"}
