github_url

:   hide

# WebRTCPeerConnectionExtension {#class_WebRTCPeerConnectionExtension}

**Inherits:**
`WebRTCPeerConnection<class_WebRTCPeerConnection>`{.interpreted-text
role="ref"} **\<** `RefCounted<class_RefCounted>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

::: {.container .contribute}
There is currently no description for this class. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-reftable-group
:::

## Methods

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Method Descriptions

::: {#class_WebRTCPeerConnectionExtension_private_method__add_ice_candidate}
::: rst-class
classref-method
:::
:::

`Error<enum_@GlobalScope_Error>`{.interpreted-text role="ref"}
**\_add_ice_candidate**(p_sdp_mid_name:
`String<class_String>`{.interpreted-text role="ref"}, p_sdp_mline_index:
`int<class_int>`{.interpreted-text role="ref"}, p_sdp_name:
`String<class_String>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__add_ice_candidate>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_WebRTCPeerConnectionExtension_private_method__close}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"} **\_close**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__close>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_WebRTCPeerConnectionExtension_private_method__create_data_channel}
::: rst-class
classref-method
:::
:::

`WebRTCDataChannel<class_WebRTCDataChannel>`{.interpreted-text
role="ref"} **\_create_data_channel**(p_label:
`String<class_String>`{.interpreted-text role="ref"}, p_config:
`Dictionary<class_Dictionary>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__create_data_channel>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_WebRTCPeerConnectionExtension_private_method__create_offer}
::: rst-class
classref-method
:::
:::

`Error<enum_@GlobalScope_Error>`{.interpreted-text role="ref"}
**\_create_offer**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__create_offer>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_WebRTCPeerConnectionExtension_private_method__get_connection_state}
::: rst-class
classref-method
:::
:::

`ConnectionState<enum_WebRTCPeerConnection_ConnectionState>`{.interpreted-text
role="ref"} **\_get_connection_state**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__get_connection_state>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_WebRTCPeerConnectionExtension_private_method__get_gathering_state}
::: rst-class
classref-method
:::
:::

`GatheringState<enum_WebRTCPeerConnection_GatheringState>`{.interpreted-text
role="ref"} **\_get_gathering_state**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__get_gathering_state>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_WebRTCPeerConnectionExtension_private_method__get_signaling_state}
::: rst-class
classref-method
:::
:::

`SignalingState<enum_WebRTCPeerConnection_SignalingState>`{.interpreted-text
role="ref"} **\_get_signaling_state**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__get_signaling_state>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_WebRTCPeerConnectionExtension_private_method__initialize}
::: rst-class
classref-method
:::
:::

`Error<enum_@GlobalScope_Error>`{.interpreted-text role="ref"}
**\_initialize**(p_config:
`Dictionary<class_Dictionary>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__initialize>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_WebRTCPeerConnectionExtension_private_method__poll}
::: rst-class
classref-method
:::
:::

`Error<enum_@GlobalScope_Error>`{.interpreted-text role="ref"}
**\_poll**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__poll>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_WebRTCPeerConnectionExtension_private_method__set_local_description}
::: rst-class
classref-method
:::
:::

`Error<enum_@GlobalScope_Error>`{.interpreted-text role="ref"}
**\_set_local_description**(p_type:
`String<class_String>`{.interpreted-text role="ref"}, p_sdp:
`String<class_String>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__set_local_description>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_WebRTCPeerConnectionExtension_private_method__set_remote_description}
::: rst-class
classref-method
:::
:::

`Error<enum_@GlobalScope_Error>`{.interpreted-text role="ref"}
**\_set_remote_description**(p_type:
`String<class_String>`{.interpreted-text role="ref"}, p_sdp:
`String<class_String>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_WebRTCPeerConnectionExtension_private_method__set_remote_description>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::
