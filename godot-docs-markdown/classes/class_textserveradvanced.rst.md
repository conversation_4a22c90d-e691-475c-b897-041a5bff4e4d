github_url

:   hide

# TextServerAdvanced {#class_TextServerAdvanced}

**Inherits:**
`TextServerExtension<class_TextServerExtension>`{.interpreted-text
role="ref"} **\<** `TextServer<class_TextServer>`{.interpreted-text
role="ref"} **\<** `RefCounted<class_RefCounted>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

An advanced text server with support for BiDi, complex text layout, and
contextual OpenType features. Used in Godot by default.

::: rst-class
classref-introduction-group
:::

## Description

An implementation of `TextServer<class_TextServer>`{.interpreted-text
role="ref"} that uses HarfBuzz, ICU and SIL Graphite to support BiDi,
complex text layouts and contextual OpenType features. This is Godot\'s
default primary `TextServer<class_TextServer>`{.interpreted-text
role="ref"} interface.
