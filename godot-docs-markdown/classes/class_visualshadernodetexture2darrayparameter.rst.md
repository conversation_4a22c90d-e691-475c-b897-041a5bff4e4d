github_url

:   hide

# VisualShaderNodeTexture2DArrayParameter {#class_VisualShaderNodeTexture2DArrayParameter}

**Inherits:**
`VisualShaderNodeTextureParameter<class_VisualShaderNodeTextureParameter>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNodeParameter<class_VisualShaderNodeParameter>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A visual shader node for shader parameter (uniform) of type
`Texture2DArray<class_Texture2DArray>`{.interpreted-text role="ref"}.

::: rst-class
classref-introduction-group
:::

## Description

This parameter allows to provide a collection of textures for the
shader. You can use
`VisualShaderNodeTexture2DArray<class_VisualShaderNodeTexture2DArray>`{.interpreted-text
role="ref"} to extract the textures from array.
