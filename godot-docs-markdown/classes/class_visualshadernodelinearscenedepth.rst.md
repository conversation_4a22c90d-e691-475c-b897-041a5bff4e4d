github_url

:   hide

# VisualShaderNodeLinearSceneDepth {#class_VisualShaderNodeLinearSceneDepth}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A visual shader node that returns the depth value of the DEPTH_TEXTURE
node in a linear space.

::: rst-class
classref-introduction-group
:::

## Description

This node can be used in fragment shaders.
