github_url

:   hide

# VisualShaderNodeParticleSphereEmitter {#class_VisualShaderNodeParticleSphereEmitter}

**Inherits:**
`VisualShaderNodeParticleEmitter<class_VisualShaderNodeParticleEmitter>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A visual shader node that makes particles emitted in a sphere shape.

::: rst-class
classref-introduction-group
:::

## Description

`VisualShaderNodeParticleEmitter<class_VisualShaderNodeParticleEmitter>`{.interpreted-text
role="ref"} that makes the particles emitted in sphere shape with the
specified inner and outer radii.
