github_url

:   hide

# VisualShaderNodeCurveXYZTexture {#class_VisualShaderNodeCurveXYZTexture}

**Inherits:**
`VisualShaderNodeResizableBase<class_VisualShaderNodeResizableBase>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Performs a `CurveXYZTexture<class_CurveXYZTexture>`{.interpreted-text
role="ref"} lookup within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Comes with a built-in editor for texture\'s curves.

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Property Descriptions

::: {#class_VisualShaderNodeCurveXYZTexture_property_texture}
::: rst-class
classref-property
:::
:::

`CurveXYZTexture<class_CurveXYZTexture>`{.interpreted-text role="ref"}
**texture**
`🔗<class_VisualShaderNodeCurveXYZTexture_property_texture>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_texture**(value:
    `CurveXYZTexture<class_CurveXYZTexture>`{.interpreted-text
    role="ref"})
-   `CurveXYZTexture<class_CurveXYZTexture>`{.interpreted-text
    role="ref"} **get_texture**()

The source texture.
