github_url

:   hide

# VisualShaderNodeScreenNormalWorldSpace {#class_VisualShaderNodeScreenNormalWorldSpace}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A visual shader node that unpacks the screen normal texture in World
Space.

::: rst-class
classref-introduction-group
:::

## Description

The ScreenNormalWorldSpace node allows to create outline effects.
