github_url

:   hide

# VisualShaderNodeSDFRaymarch {#class_VisualShaderNodeSDFRaymarch}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

SDF raymarching algorithm to be used within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Casts a ray against the screen SDF (signed-distance field) and returns
the distance travelled.
