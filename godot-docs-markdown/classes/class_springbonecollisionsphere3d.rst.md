github_url

:   hide

# SpringBoneCollisionSphere3D {#class_SpringBoneCollisionSphere3D}

**Inherits:**
`SpringBoneCollision3D<class_SpringBoneCollision3D>`{.interpreted-text
role="ref"} **\<** `Node3D<class_Node3D>`{.interpreted-text role="ref"}
**\<** `Node<class_Node>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A sphere shape collision that interacts with
`SpringBoneSimulator3D<class_SpringBoneSimulator3D>`{.interpreted-text
role="ref"}.

::: rst-class
classref-introduction-group
:::

## Description

A sphere shape collision that interacts with
`SpringBoneSimulator3D<class_SpringBoneSimulator3D>`{.interpreted-text
role="ref"}.

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Property Descriptions

::: {#class_SpringBoneCollisionSphere3D_property_inside}
::: rst-class
classref-property
:::
:::

`bool<class_bool>`{.interpreted-text role="ref"} **inside** = `false`
`🔗<class_SpringBoneCollisionSphere3D_property_inside>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_inside**(value: `bool<class_bool>`{.interpreted-text
    role="ref"})
-   `bool<class_bool>`{.interpreted-text role="ref"} **is_inside**()

If `true`, the collision acts to trap the joint within the collision.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_SpringBoneCollisionSphere3D_property_radius}
::: rst-class
classref-property
:::
:::

`float<class_float>`{.interpreted-text role="ref"} **radius** = `0.1`
`🔗<class_SpringBoneCollisionSphere3D_property_radius>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_radius**(value: `float<class_float>`{.interpreted-text
    role="ref"})
-   `float<class_float>`{.interpreted-text role="ref"} **get_radius**()

The sphere\'s radius.
