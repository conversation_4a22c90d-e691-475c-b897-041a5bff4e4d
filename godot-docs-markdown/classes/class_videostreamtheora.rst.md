github_url

:   hide

# VideoStreamTheora {#class_VideoStreamTheora}

**Inherits:** `VideoStream<class_VideoStream>`{.interpreted-text
role="ref"} **\<** `Resource<class_Resource>`{.interpreted-text
role="ref"} **\<** `RefCounted<class_RefCounted>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

`VideoStream<class_VideoStream>`{.interpreted-text role="ref"} resource
for Ogg Theora videos.

::: rst-class
classref-introduction-group
:::

## Description

`VideoStream<class_VideoStream>`{.interpreted-text role="ref"} resource
handling the [Ogg Theora](https://www.theora.org/) video format with
`.ogv` extension. The Theora codec is decoded on the CPU.

**Note:** While Ogg Theora videos can also have an `.ogg` extension, you
will have to rename the extension to `.ogv` to use those videos within
Godot.
