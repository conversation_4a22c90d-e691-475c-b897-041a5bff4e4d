github_url

:   hide

# VisualShaderNodeDotProduct {#class_VisualShaderNodeDotProduct}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Calculates a dot product of two vectors within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Translates to `dot(a, b)` in the shader language.
