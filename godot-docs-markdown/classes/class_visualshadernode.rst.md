github_url

:   hide

# VisualShaderNode {#class_VisualShaderNode}

**Inherits:** `Resource<class_Resource>`{.interpreted-text role="ref"}
**\<** `RefCounted<class_RefCounted>`{.interpreted-text role="ref"}
**\<** `Object<class_Object>`{.interpreted-text role="ref"}

**Inherited By:**
`VisualShaderNodeBillboard<class_VisualShaderNodeBillboard>`{.interpreted-text
role="ref"},
`VisualShaderNodeClamp<class_VisualShaderNodeClamp>`{.interpreted-text
role="ref"},
`VisualShaderNodeColorFunc<class_VisualShaderNodeColorFunc>`{.interpreted-text
role="ref"},
`VisualShaderNodeColorOp<class_VisualShaderNodeColorOp>`{.interpreted-text
role="ref"},
`VisualShaderNodeCompare<class_VisualShaderNodeCompare>`{.interpreted-text
role="ref"},
`VisualShaderNodeConstant<class_VisualShaderNodeConstant>`{.interpreted-text
role="ref"},
`VisualShaderNodeCubemap<class_VisualShaderNodeCubemap>`{.interpreted-text
role="ref"},
`VisualShaderNodeCustom<class_VisualShaderNodeCustom>`{.interpreted-text
role="ref"},
`VisualShaderNodeDerivativeFunc<class_VisualShaderNodeDerivativeFunc>`{.interpreted-text
role="ref"},
`VisualShaderNodeDeterminant<class_VisualShaderNodeDeterminant>`{.interpreted-text
role="ref"},
`VisualShaderNodeDistanceFade<class_VisualShaderNodeDistanceFade>`{.interpreted-text
role="ref"},
`VisualShaderNodeDotProduct<class_VisualShaderNodeDotProduct>`{.interpreted-text
role="ref"},
`VisualShaderNodeFloatFunc<class_VisualShaderNodeFloatFunc>`{.interpreted-text
role="ref"},
`VisualShaderNodeFloatOp<class_VisualShaderNodeFloatOp>`{.interpreted-text
role="ref"},
`VisualShaderNodeFresnel<class_VisualShaderNodeFresnel>`{.interpreted-text
role="ref"},
`VisualShaderNodeIf<class_VisualShaderNodeIf>`{.interpreted-text
role="ref"},
`VisualShaderNodeInput<class_VisualShaderNodeInput>`{.interpreted-text
role="ref"},
`VisualShaderNodeIntFunc<class_VisualShaderNodeIntFunc>`{.interpreted-text
role="ref"},
`VisualShaderNodeIntOp<class_VisualShaderNodeIntOp>`{.interpreted-text
role="ref"},
`VisualShaderNodeIs<class_VisualShaderNodeIs>`{.interpreted-text
role="ref"},
`VisualShaderNodeLinearSceneDepth<class_VisualShaderNodeLinearSceneDepth>`{.interpreted-text
role="ref"},
`VisualShaderNodeMix<class_VisualShaderNodeMix>`{.interpreted-text
role="ref"},
`VisualShaderNodeMultiplyAdd<class_VisualShaderNodeMultiplyAdd>`{.interpreted-text
role="ref"},
`VisualShaderNodeOuterProduct<class_VisualShaderNodeOuterProduct>`{.interpreted-text
role="ref"},
`VisualShaderNodeOutput<class_VisualShaderNodeOutput>`{.interpreted-text
role="ref"},
`VisualShaderNodeParameter<class_VisualShaderNodeParameter>`{.interpreted-text
role="ref"},
`VisualShaderNodeParameterRef<class_VisualShaderNodeParameterRef>`{.interpreted-text
role="ref"},
`VisualShaderNodeParticleAccelerator<class_VisualShaderNodeParticleAccelerator>`{.interpreted-text
role="ref"},
`VisualShaderNodeParticleConeVelocity<class_VisualShaderNodeParticleConeVelocity>`{.interpreted-text
role="ref"},
`VisualShaderNodeParticleEmit<class_VisualShaderNodeParticleEmit>`{.interpreted-text
role="ref"},
`VisualShaderNodeParticleEmitter<class_VisualShaderNodeParticleEmitter>`{.interpreted-text
role="ref"},
`VisualShaderNodeParticleMultiplyByAxisAngle<class_VisualShaderNodeParticleMultiplyByAxisAngle>`{.interpreted-text
role="ref"},
`VisualShaderNodeParticleRandomness<class_VisualShaderNodeParticleRandomness>`{.interpreted-text
role="ref"},
`VisualShaderNodeProximityFade<class_VisualShaderNodeProximityFade>`{.interpreted-text
role="ref"},
`VisualShaderNodeRandomRange<class_VisualShaderNodeRandomRange>`{.interpreted-text
role="ref"},
`VisualShaderNodeRemap<class_VisualShaderNodeRemap>`{.interpreted-text
role="ref"},
`VisualShaderNodeReroute<class_VisualShaderNodeReroute>`{.interpreted-text
role="ref"},
`VisualShaderNodeResizableBase<class_VisualShaderNodeResizableBase>`{.interpreted-text
role="ref"},
`VisualShaderNodeRotationByAxis<class_VisualShaderNodeRotationByAxis>`{.interpreted-text
role="ref"},
`VisualShaderNodeSample3D<class_VisualShaderNodeSample3D>`{.interpreted-text
role="ref"},
`VisualShaderNodeScreenNormalWorldSpace<class_VisualShaderNodeScreenNormalWorldSpace>`{.interpreted-text
role="ref"},
`VisualShaderNodeScreenUVToSDF<class_VisualShaderNodeScreenUVToSDF>`{.interpreted-text
role="ref"},
`VisualShaderNodeSDFRaymarch<class_VisualShaderNodeSDFRaymarch>`{.interpreted-text
role="ref"},
`VisualShaderNodeSDFToScreenUV<class_VisualShaderNodeSDFToScreenUV>`{.interpreted-text
role="ref"},
`VisualShaderNodeSmoothStep<class_VisualShaderNodeSmoothStep>`{.interpreted-text
role="ref"},
`VisualShaderNodeStep<class_VisualShaderNodeStep>`{.interpreted-text
role="ref"},
`VisualShaderNodeSwitch<class_VisualShaderNodeSwitch>`{.interpreted-text
role="ref"},
`VisualShaderNodeTexture<class_VisualShaderNodeTexture>`{.interpreted-text
role="ref"},
`VisualShaderNodeTextureSDF<class_VisualShaderNodeTextureSDF>`{.interpreted-text
role="ref"},
`VisualShaderNodeTextureSDFNormal<class_VisualShaderNodeTextureSDFNormal>`{.interpreted-text
role="ref"},
`VisualShaderNodeTransformCompose<class_VisualShaderNodeTransformCompose>`{.interpreted-text
role="ref"},
`VisualShaderNodeTransformDecompose<class_VisualShaderNodeTransformDecompose>`{.interpreted-text
role="ref"},
`VisualShaderNodeTransformFunc<class_VisualShaderNodeTransformFunc>`{.interpreted-text
role="ref"},
`VisualShaderNodeTransformOp<class_VisualShaderNodeTransformOp>`{.interpreted-text
role="ref"},
`VisualShaderNodeTransformVecMult<class_VisualShaderNodeTransformVecMult>`{.interpreted-text
role="ref"},
`VisualShaderNodeUIntFunc<class_VisualShaderNodeUIntFunc>`{.interpreted-text
role="ref"},
`VisualShaderNodeUIntOp<class_VisualShaderNodeUIntOp>`{.interpreted-text
role="ref"},
`VisualShaderNodeUVFunc<class_VisualShaderNodeUVFunc>`{.interpreted-text
role="ref"},
`VisualShaderNodeUVPolarCoord<class_VisualShaderNodeUVPolarCoord>`{.interpreted-text
role="ref"},
`VisualShaderNodeVarying<class_VisualShaderNodeVarying>`{.interpreted-text
role="ref"},
`VisualShaderNodeVectorBase<class_VisualShaderNodeVectorBase>`{.interpreted-text
role="ref"},
`VisualShaderNodeWorldPositionFromDepth<class_VisualShaderNodeWorldPositionFromDepth>`{.interpreted-text
role="ref"}

Base class for `VisualShader<class_VisualShader>`{.interpreted-text
role="ref"} nodes. Not related to scene nodes.

::: rst-class
classref-introduction-group
:::

## Description

Visual shader graphs consist of various nodes. Each node in the graph is
a separate object and they are represented as a rectangular boxes with
title and a set of properties. Each node also has connection ports that
allow to connect it to another nodes and control the flow of the shader.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `Using VisualShaders <../tutorials/shaders/visual_shaders>`{.interpreted-text
    role="doc"}

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-reftable-group
:::

## Methods

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Enumerations

::: {#enum_VisualShaderNode_PortType}
::: rst-class
classref-enumeration
:::
:::

enum **PortType**:
`🔗<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}

::: {#class_VisualShaderNode_constant_PORT_TYPE_SCALAR}
::: rst-class
classref-enumeration-constant
:::
:::

`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
**PORT_TYPE_SCALAR** = `0`

Floating-point scalar. Translated to `float` type in shader code.

::: {#class_VisualShaderNode_constant_PORT_TYPE_SCALAR_INT}
::: rst-class
classref-enumeration-constant
:::
:::

`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
**PORT_TYPE_SCALAR_INT** = `1`

Integer scalar. Translated to `int` type in shader code.

::: {#class_VisualShaderNode_constant_PORT_TYPE_SCALAR_UINT}
::: rst-class
classref-enumeration-constant
:::
:::

`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
**PORT_TYPE_SCALAR_UINT** = `2`

Unsigned integer scalar. Translated to `uint` type in shader code.

::: {#class_VisualShaderNode_constant_PORT_TYPE_VECTOR_2D}
::: rst-class
classref-enumeration-constant
:::
:::

`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
**PORT_TYPE_VECTOR_2D** = `3`

2D vector of floating-point values. Translated to `vec2` type in shader
code.

::: {#class_VisualShaderNode_constant_PORT_TYPE_VECTOR_3D}
::: rst-class
classref-enumeration-constant
:::
:::

`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
**PORT_TYPE_VECTOR_3D** = `4`

3D vector of floating-point values. Translated to `vec3` type in shader
code.

::: {#class_VisualShaderNode_constant_PORT_TYPE_VECTOR_4D}
::: rst-class
classref-enumeration-constant
:::
:::

`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
**PORT_TYPE_VECTOR_4D** = `5`

4D vector of floating-point values. Translated to `vec4` type in shader
code.

::: {#class_VisualShaderNode_constant_PORT_TYPE_BOOLEAN}
::: rst-class
classref-enumeration-constant
:::
:::

`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
**PORT_TYPE_BOOLEAN** = `6`

Boolean type. Translated to `bool` type in shader code.

::: {#class_VisualShaderNode_constant_PORT_TYPE_TRANSFORM}
::: rst-class
classref-enumeration-constant
:::
:::

`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
**PORT_TYPE_TRANSFORM** = `7`

Transform type. Translated to `mat4` type in shader code.

::: {#class_VisualShaderNode_constant_PORT_TYPE_SAMPLER}
::: rst-class
classref-enumeration-constant
:::
:::

`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
**PORT_TYPE_SAMPLER** = `8`

Sampler type. Translated to reference of sampler uniform in shader code.
Can only be used for input ports in non-uniform nodes.

::: {#class_VisualShaderNode_constant_PORT_TYPE_MAX}
::: rst-class
classref-enumeration-constant
:::
:::

`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
**PORT_TYPE_MAX** = `9`

Represents the size of the
`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text role="ref"}
enum.

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Property Descriptions

::: {#class_VisualShaderNode_property_linked_parent_graph_frame}
::: rst-class
classref-property
:::
:::

`int<class_int>`{.interpreted-text role="ref"}
**linked_parent_graph_frame** = `-1`
`🔗<class_VisualShaderNode_property_linked_parent_graph_frame>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_frame**(value: `int<class_int>`{.interpreted-text role="ref"})
-   `int<class_int>`{.interpreted-text role="ref"} **get_frame**()

Represents the index of the frame this node is linked to. If set to `-1`
the node is not linked to any frame.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_VisualShaderNode_property_output_port_for_preview}
::: rst-class
classref-property
:::
:::

`int<class_int>`{.interpreted-text role="ref"}
**output_port_for_preview** = `-1`
`🔗<class_VisualShaderNode_property_output_port_for_preview>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_output_port_for_preview**(value:
    `int<class_int>`{.interpreted-text role="ref"})
-   `int<class_int>`{.interpreted-text role="ref"}
    **get_output_port_for_preview**()

Sets the output port index which will be showed for preview. If set to
`-1` no port will be open for preview.

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Method Descriptions

::: {#class_VisualShaderNode_method_clear_default_input_values}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**clear_default_input_values**()
`🔗<class_VisualShaderNode_method_clear_default_input_values>`{.interpreted-text
role="ref"}

Clears the default input ports value.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_VisualShaderNode_method_get_default_input_port}
::: rst-class
classref-method
:::
:::

`int<class_int>`{.interpreted-text role="ref"}
**get_default_input_port**(type:
`PortType<enum_VisualShaderNode_PortType>`{.interpreted-text
role="ref"})
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_VisualShaderNode_method_get_default_input_port>`{.interpreted-text
role="ref"}

Returns the input port which should be connected by default when this
node is created as a result of dragging a connection from an existing
node to the empty space on the graph.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_VisualShaderNode_method_get_default_input_values}
::: rst-class
classref-method
:::
:::

`Array<class_Array>`{.interpreted-text role="ref"}
**get_default_input_values**()
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_VisualShaderNode_method_get_default_input_values>`{.interpreted-text
role="ref"}

Returns an `Array<class_Array>`{.interpreted-text role="ref"} containing
default values for all of the input ports of the node in the form
`[index0, value0, index1, value1, ...]`.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_VisualShaderNode_method_get_input_port_default_value}
::: rst-class
classref-method
:::
:::

`Variant<class_Variant>`{.interpreted-text role="ref"}
**get_input_port_default_value**(port:
`int<class_int>`{.interpreted-text role="ref"})
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_VisualShaderNode_method_get_input_port_default_value>`{.interpreted-text
role="ref"}

Returns the default value of the input `port`.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_VisualShaderNode_method_remove_input_port_default_value}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**remove_input_port_default_value**(port:
`int<class_int>`{.interpreted-text role="ref"})
`🔗<class_VisualShaderNode_method_remove_input_port_default_value>`{.interpreted-text
role="ref"}

Removes the default value of the input `port`.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_VisualShaderNode_method_set_default_input_values}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**set_default_input_values**(values:
`Array<class_Array>`{.interpreted-text role="ref"})
`🔗<class_VisualShaderNode_method_set_default_input_values>`{.interpreted-text
role="ref"}

Sets the default input ports values using an
`Array<class_Array>`{.interpreted-text role="ref"} of the form
`[index0, value0, index1, value1, ...]`. For example:
`[0, Vector3(0, 0, 0), 1, Vector3(0, 0, 0)]`.

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_VisualShaderNode_method_set_input_port_default_value}
::: rst-class
classref-method
:::
:::

`void (No return value.)`{.interpreted-text role="abbr"}
**set_input_port_default_value**(port:
`int<class_int>`{.interpreted-text role="ref"}, value:
`Variant<class_Variant>`{.interpreted-text role="ref"}, prev_value:
`Variant<class_Variant>`{.interpreted-text role="ref"} = null)
`🔗<class_VisualShaderNode_method_set_input_port_default_value>`{.interpreted-text
role="ref"}

Sets the default `value` for the selected input `port`.
