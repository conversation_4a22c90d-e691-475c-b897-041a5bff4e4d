github_url

:   hide

# VisualShaderNodeParticleConeVelocity {#class_VisualShaderNodeParticleConeVelocity}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A visual shader node that makes particles move in a cone shape.

::: rst-class
classref-introduction-group
:::

## Description

This node can be used in \"start\" step of particle shader. It defines
the initial velocity of the particles, making them move in cone shape
starting from the center, with a given spread.
