github_url

:   hide

# VisualShaderNodeConstant {#class_VisualShaderNodeConstant}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

**Inherited By:**
`VisualShaderNodeBooleanConstant<class_VisualShaderNodeBooleanConstant>`{.interpreted-text
role="ref"},
`VisualShaderNodeColorConstant<class_VisualShaderNodeColorConstant>`{.interpreted-text
role="ref"},
`VisualShaderNodeFloatConstant<class_VisualShaderNodeFloatConstant>`{.interpreted-text
role="ref"},
`VisualShaderNodeIntConstant<class_VisualShaderNodeIntConstant>`{.interpreted-text
role="ref"},
`VisualShaderNodeTransformConstant<class_VisualShaderNodeTransformConstant>`{.interpreted-text
role="ref"},
`VisualShaderNodeUIntConstant<class_VisualShaderNodeUIntConstant>`{.interpreted-text
role="ref"},
`VisualShaderNodeVec2Constant<class_VisualShaderNodeVec2Constant>`{.interpreted-text
role="ref"},
`VisualShaderNodeVec3Constant<class_VisualShaderNodeVec3Constant>`{.interpreted-text
role="ref"},
`VisualShaderNodeVec4Constant<class_VisualShaderNodeVec4Constant>`{.interpreted-text
role="ref"}

A base type for the constants within the visual shader graph.

::: rst-class
classref-introduction-group
:::

## Description

This is an abstract class. See the derived types for descriptions of the
possible values.
