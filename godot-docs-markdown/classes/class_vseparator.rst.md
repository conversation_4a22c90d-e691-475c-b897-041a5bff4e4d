github_url

:   hide

# VSeparator {#class_VSeparator}

**Inherits:** `Separator<class_Separator>`{.interpreted-text role="ref"}
**\<** `Control<class_Control>`{.interpreted-text role="ref"} **\<**
`CanvasItem<class_CanvasItem>`{.interpreted-text role="ref"} **\<**
`Node<class_Node>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

A vertical line used for separating other controls.

::: rst-class
classref-introduction-group
:::

## Description

A vertical separator used for separating other controls that are
arranged **horizontally**. **VSeparator** is purely visual and normally
drawn as a `StyleBoxLine<class_StyleBoxLine>`{.interpreted-text
role="ref"}.
