github_url

:   hide

# ResourceImporterImage {#class_ResourceImporterImage}

**Inherits:**
`ResourceImporter<class_ResourceImporter>`{.interpreted-text role="ref"}
**\<** `RefCounted<class_RefCounted>`{.interpreted-text role="ref"}
**\<** `Object<class_Object>`{.interpreted-text role="ref"}

Imports a image for use in scripting, with no rendering capabilities.

::: rst-class
classref-introduction-group
:::

## Description

This importer imports `Image<class_Image>`{.interpreted-text role="ref"}
resources, as opposed to
`CompressedTexture2D<class_CompressedTexture2D>`{.interpreted-text
role="ref"}. If you need to render the image in 2D or 3D, use
`ResourceImporterTexture<class_ResourceImporterTexture>`{.interpreted-text
role="ref"} instead.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `Importing images <../tutorials/assets_pipeline/importing_images>`{.interpreted-text
    role="doc"}
