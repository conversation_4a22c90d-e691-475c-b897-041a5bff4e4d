github_url

:   hide

# VSplitContainer {#class_VSplitContainer}

**Inherits:** `SplitContainer<class_SplitContainer>`{.interpreted-text
role="ref"} **\<** `Container<class_Container>`{.interpreted-text
role="ref"} **\<** `Control<class_Control>`{.interpreted-text
role="ref"} **\<** `CanvasItem<class_CanvasItem>`{.interpreted-text
role="ref"} **\<** `Node<class_Node>`{.interpreted-text role="ref"}
**\<** `Object<class_Object>`{.interpreted-text role="ref"}

A container that splits two child controls vertically and provides a
grabber for adjusting the split ratio.

::: rst-class
classref-introduction-group
:::

## Description

A container that accepts only two child controls, then arranges them
vertically and creates a divisor between them. The divisor can be
dragged around to change the size relation between the child controls.

::: rst-class
classref-introduction-group
:::

## Tutorials

-   `Using Containers <../tutorials/ui/gui_containers>`{.interpreted-text
    role="doc"}
