github_url

:   hide

# VisualShaderNodeComment {#class_VisualShaderNodeComment}

**Deprecated:** This class has no function anymore and only exists for
compatibility.

**Inherits:**
`VisualShaderNodeFrame<class_VisualShaderNodeFrame>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNodeResizableBase<class_VisualShaderNodeResizableBase>`{.interpreted-text
role="ref"} **\<**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Only exists for compatibility. Use
`VisualShaderNodeFrame<class_VisualShaderNodeFrame>`{.interpreted-text
role="ref"} as a replacement.

::: rst-class
classref-introduction-group
:::

## Description

This node was replaced by
`VisualShaderNodeFrame<class_VisualShaderNodeFrame>`{.interpreted-text
role="ref"} and only exists to preserve compatibility. In the
`VisualShader<class_VisualShader>`{.interpreted-text role="ref"} editor
it behaves exactly like
`VisualShaderNodeFrame<class_VisualShaderNodeFrame>`{.interpreted-text
role="ref"}.

::: rst-class
classref-reftable-group
:::

## Properties

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Property Descriptions

::: {#class_VisualShaderNodeComment_property_description}
::: rst-class
classref-property
:::
:::

`String<class_String>`{.interpreted-text role="ref"} **description** =
`""`
`🔗<class_VisualShaderNodeComment_property_description>`{.interpreted-text
role="ref"}

::: rst-class
classref-property-setget
:::

-   `void (No return value.)`{.interpreted-text role="abbr"}
    **set_description**(value: `String<class_String>`{.interpreted-text
    role="ref"})
-   `String<class_String>`{.interpreted-text role="ref"}
    **get_description**()

This property only exists to preserve data authored in earlier versions
of Godot. It has currently no function.
