github_url

:   hide

# PacketPeerExtension {#class_PacketPeerExtension}

**Inherits:** `PacketPeer<class_PacketPeer>`{.interpreted-text
role="ref"} **\<** `RefCounted<class_RefCounted>`{.interpreted-text
role="ref"} **\<** `Object<class_Object>`{.interpreted-text role="ref"}

::: {.container .contribute}
There is currently no description for this class. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-reftable-group
:::

## Methods

::: rst-class
classref-section-separator
:::

------------------------------------------------------------------------

::: rst-class
classref-descriptions-group
:::

## Method Descriptions

::: {#class_PacketPeerExtension_private_method__get_available_packet_count}
::: rst-class
classref-method
:::
:::

`int<class_int>`{.interpreted-text role="ref"}
**\_get_available_packet_count**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_PacketPeerExtension_private_method__get_available_packet_count>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_PacketPeerExtension_private_method__get_max_packet_size}
::: rst-class
classref-method
:::
:::

`int<class_int>`{.interpreted-text role="ref"}
**\_get_max_packet_size**()
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`const (This method has no side effects. It doesn't modify any of the instance's member variables.)`{.interpreted-text
role="abbr"}
`🔗<class_PacketPeerExtension_private_method__get_max_packet_size>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_PacketPeerExtension_private_method__get_packet}
::: rst-class
classref-method
:::
:::

`Error<enum_@GlobalScope_Error>`{.interpreted-text role="ref"}
**\_get_packet**(r_buffer: `const uint8_t **`, r_buffer_size:
`int32_t*`)
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_PacketPeerExtension_private_method__get_packet>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::

::: rst-class
classref-item-separator
:::

------------------------------------------------------------------------

::: {#class_PacketPeerExtension_private_method__put_packet}
::: rst-class
classref-method
:::
:::

`Error<enum_@GlobalScope_Error>`{.interpreted-text role="ref"}
**\_put_packet**(p_buffer: `const uint8_t*`, p_buffer_size:
`int<class_int>`{.interpreted-text role="ref"})
`virtual (This method should typically be overridden by the user to have any effect.)`{.interpreted-text
role="abbr"}
`🔗<class_PacketPeerExtension_private_method__put_packet>`{.interpreted-text
role="ref"}

::: {.container .contribute}
There is currently no description for this method. Please help us by
`contributing one <doc_updating_the_class_reference>`{.interpreted-text
role="ref"}!
:::
