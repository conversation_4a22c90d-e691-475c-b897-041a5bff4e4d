github_url

:   hide

# VisualShaderNodeTextureSDF {#class_VisualShaderNodeTextureSDF}

**Inherits:**
`VisualShaderNode<class_VisualShaderNode>`{.interpreted-text role="ref"}
**\<** `Resource<class_Resource>`{.interpreted-text role="ref"} **\<**
`RefCounted<class_RefCounted>`{.interpreted-text role="ref"} **\<**
`Object<class_Object>`{.interpreted-text role="ref"}

Performs an SDF (signed-distance field) texture lookup within the visual
shader graph.

::: rst-class
classref-introduction-group
:::

## Description

Translates to `texture_sdf(sdf_pos)` in the shader language.
