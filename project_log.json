{"project_name": "Godot Advanced FPS Controller Template", "description": "A template for creating advanced first-person shooter games in Godot 4", "version": "1.0.0", "last_updated": "2023-11-15", "scenes": [{"path": "Scenes/EnemyScene.tscn", "uid": "uid://dxnqvnxvqnbgr", "description": "Enemy character with AI behavior"}, {"path": "Scenes/HealthHUD.tscn", "uid": "uid://c8b42uxl5rj2v", "description": "Health and armor HUD elements"}, {"path": "Scenes/InputKeybindOptionScene.tscn", "uid": "uid://bvxqnwqxvqnbgr", "description": "Input keybinding options UI"}, {"path": "Scenes/OptionsMenuScene.tscn", "uid": "uid://bdhpuj7itpv6j", "description": "Game options menu"}, {"path": "Scenes/PauseMenuScene.tscn", "uid": "uid://dh3ln04ijax8n", "description": "Pause menu UI"}, {"path": "Scenes/PlayerCharacterScene.tscn", "uid": "uid://bwggrf7sbmkcv", "description": "Player character with movement, camera, and input handling"}, {"path": "Scenes/TemplateMapScene.tscn", "uid": "uid://b7b4tcr8q1rg7", "description": "Template map for creating new levels"}, {"path": "Scenes/TestMapScene_new.tscn", "uid": "uid://b7b4tcr8q1rg7", "description": "Test map with player, pickups, and environment"}, {"path": "Scenes/WeaponHUD.tscn", "uid": "uid://dqnvyf8h5xgbj", "description": "Weapon HUD elements"}, {"path": "Scenes/WeaponTestScene.tscn", "uid": "uid://bvxqnwqxvqnbgr", "description": "Test scene for weapons"}], "scripts": [{"path": "Scripts/PlayerCharacter/PlayerCharacterScript.gd", "description": "Main player character controller script"}, {"path": "Scripts/PlayerCharacter/CameraScript.gd", "description": "Camera controller for player character"}, {"path": "Scripts/PlayerCharacter/HealthSystem.gd", "description": "Health and armor system for player character"}, {"path": "Scripts/PlayerCharacter/HealthHUDUpdater.gd", "description": "Updates HUD elements based on health system"}, {"path": "Scripts/Weapons/WeaponManager.gd", "description": "Manages weapon inventory and switching"}, {"path": "Scripts/Weapons/WeaponPickup.gd", "description": "Pickup script for weapons and ammo"}, {"path": "Scripts/Weapons/WeaponHUD.gd", "description": "Updates HUD elements for weapons"}, {"path": "Scripts/Pickups/HealthPickup.gd", "description": "Pickup script for health items"}, {"path": "Scripts/Pickups/ArmorPickup.gd", "description": "Pickup script for armor items"}, {"path": "Scripts/Enemies/EnemyAI.gd", "description": "AI controller for enemy characters"}, {"path": "Scripts/Others/ObjectToolScript.gd", "description": "Script for interactive tools like grappling hook and knockback tool"}, {"path": "Scripts/PlayerCharacter/UI/HUDScript.gd", "description": "Main HUD controller"}, {"path": "Scripts/PlayerCharacter/UI/PauseMenuScript.gd", "description": "Pause menu controller"}], "collision_layers": {"1": "structures", "2": "<PERSON><PERSON><PERSON><PERSON>", "3": "walkableWalls", "4": "pickups"}, "input_actions": {"moveLeft": "<PERSON>, <PERSON> Arrow", "moveRight": "<PERSON>, <PERSON> Arrow", "moveBackward": "S, Down Arrow", "moveForward": "<PERSON>, <PERSON>", "jump": "Space", "run": "Shift", "crouch | slide": "C", "dash": "Alt", "grappleHook": "G", "pauseMenu": "Escape", "useKnockbackTool": "H", "weapon_1": "1", "weapon_2": "2", "weapon_3": "3", "weapon_4": "4", "weapon_5": "5", "weapon_next": "Mouse Wheel Up", "weapon_prev": "Mouse Wheel Down", "primary_fire": "Left <PERSON>", "secondary_fire": "Right <PERSON>", "reload": "R"}, "known_issues": ["Mouse look may not work if PauseMenu is not properly referenced", "Pickups may not work if collision layers are not properly set", "UUID duplicates can cause issues with scene references"], "fixes_applied": ["Fixed UUID duplicates between HealthHUD.tscn, EnemyScene.tscn, and WeaponTestScene.tscn", "Fixed mouse look by making CameraScript.gd handle missing PauseMenu reference", "Added debug output to WeaponPickup.gd to help diagnose pickup issues", "Updated key bindings for grappleHook (G) and useKnockbackTool (H)", "Fixed unused parameter warnings in HealthSystem.gd and HealthHUDUpdater.gd"]}