@tool
extends EditorScript

## Test script for WeaponDatabase functionality
## Run this script to verify that the WeaponDatabase autoload works correctly

func _run():
	print("=== Testing WeaponDatabase Functionality ===")
	
	# Test basic functionality
	test_weapon_registration()
	test_weapon_addition()
	test_ammo_management()
	test_dual_wield_functionality()
	
	print("=== WeaponDatabase Tests Completed ===")

func test_weapon_registration():
	print("\n--- Testing Weapon Registration ---")
	
	# Access the WeaponDatabase autoload
	var weapon_db = get_node("/root/WeaponDB")
	if not weapon_db:
		print("ERROR: WeaponDatabase autoload not found!")
		return
	
	print("✓ WeaponDatabase autoload found")
	
	# Test registering a weapon
	var weapon_data = weapon_db.register_weapon("TestPistol", 100, 15)
	if weapon_data:
		print("✓ Weapon registration successful: ", weapon_data.weapon_name)
	else:
		print("✗ Weapon registration failed")

func test_weapon_addition():
	print("\n--- Testing Weapon Addition ---")
	
	var weapon_db = get_node("/root/WeaponDB")
	if not weapon_db:
		return
	
	# Test adding a weapon to inventory
	var weapon_data = weapon_db.add_weapon("TestRifle", 30, 90, 150, 30)
	if weapon_data and weapon_data.is_owned:
		print("✓ Weapon addition successful: ", weapon_data.weapon_name)
		print("  Current ammo: ", weapon_data.current_ammo)
		print("  Reserve ammo: ", weapon_data.reserve_ammo)
	else:
		print("✗ Weapon addition failed")

func test_ammo_management():
	print("\n--- Testing Ammo Management ---")
	
	var weapon_db = get_node("/root/WeaponDB")
	if not weapon_db:
		return
	
	# Add ammo to existing weapon
	var added_ammo = weapon_db.add_ammo("TestRifle", 50)
	print("✓ Added ammo: ", added_ammo)
	
	# Test weapon reload
	var reload_success = weapon_db.reload_weapon("TestRifle")
	if reload_success:
		print("✓ Weapon reload successful")
	else:
		print("✗ Weapon reload failed")
	
	# Test ammo usage
	var use_success = weapon_db.use_ammo("TestRifle", 5)
	if use_success:
		print("✓ Ammo usage successful")
	else:
		print("✗ Ammo usage failed")

func test_dual_wield_functionality():
	print("\n--- Testing Dual-Wield Functionality ---")
	
	var weapon_db = get_node("/root/WeaponDB")
	if not weapon_db:
		return
	
	# Add PP8 for dual-wield testing
	weapon_db.add_weapon("PP8", 8, 32, 80, 8)
	
	# Setup dual-wield
	var dual_wield_success = weapon_db.setup_dual_wield("PP8", 8, 8, 32)
	if dual_wield_success:
		print("✓ Dual-wield setup successful")
		
		var weapon_data = weapon_db.get_weapon_data("PP8")
		if weapon_data and weapon_data.is_dual_wield:
			print("  Left clip: ", weapon_data.left_clip_ammo)
			print("  Right clip: ", weapon_data.right_clip_ammo)
			print("  Reserve: ", weapon_data.reserve_ammo)
		else:
			print("✗ Dual-wield data not found")
	else:
		print("✗ Dual-wield setup failed")

func get_node(path: String):
	# In editor script context, we need to access the autoload differently
	if Engine.is_editor_hint():
		# Try to access through the scene tree
		var main_loop = Engine.get_main_loop()
		if main_loop and main_loop.has_method("get_node"):
			return main_loop.get_node(path)
	return null
