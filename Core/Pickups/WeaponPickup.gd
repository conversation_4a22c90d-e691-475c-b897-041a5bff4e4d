extends Area3D
class_name WeaponPickup

# Pickup properties
@export var weapon_resource: PackedScene
@export var weapon_type: String = "Unknown"
@export var ammo_amount: int = 0
@export var respawn_time: float = 30.0

# Visual properties
@export_group("Visual Effects")
@export var rotate_speed: float = 1.0
@export var bob_height: float = 0.2
@export var bob_speed: float = 2.0
@export var pickup_effect: PackedScene
@export var pickup_sound: AudioStream
@export var pickup_mesh: MeshInstance3D

# State
var is_available: bool = true
var initial_position: Vector3
var time_elapsed: float = 0.0

# References
@onready var collision_shape: CollisionShape3D = $CollisionShape3D if has_node("CollisionShape3D") else null
@onready var audio_player: AudioStreamPlayer3D = $AudioStreamPlayer3D if has_node("AudioStreamPlayer3D") else null
@onready var mesh_instance: MeshInstance3D = $MeshInstance3D if has_node("MeshInstance3D") else null

# Signals
signal pickup_collected(by_player, weapon_type, ammo_amount)

func _ready():
	# Store initial position for bobbing effect
	initial_position = global_position

	# Set process mode to ensure pickup works during pause
	process_mode = Node.PROCESS_MODE_PAUSABLE

	# Connect signals
	body_entered.connect(_on_body_entered)

	# Set up collision
	collision_layer = 4  # Pickup layer
	collision_mask = 2   # Player layer

	# Ensure this is a trigger area, not a solid collision
	monitoring = true
	monitorable = false

	# Validate weapon resource
	if weapon_resource == null:
		print("ERROR: WeaponPickup " + weapon_type + " has no weapon_resource assigned!")
		queue_free()
		return

	# If pickup_mesh is not set, try to find a MeshInstance3D child
	if pickup_mesh == null and mesh_instance != null:
		pickup_mesh = mesh_instance

	print("WeaponPickup initialized: " + weapon_type + " with resource: " + str(weapon_resource))

func _process(delta):
	if is_available and pickup_mesh:
		# Rotate the pickup
		pickup_mesh.rotate_y(rotate_speed * delta)

		# Bob up and down
		time_elapsed += delta
		var bob_offset = sin(time_elapsed * bob_speed) * bob_height
		global_position = initial_position + Vector3(0, bob_offset, 0)

func _on_body_entered(body):
	print("Body entered pickup: ", body.name, " - Available: ", is_available)

	if !is_available:
		print("Pickup not available")
		return

	# Check if the body is a player character
	if body is PlayerController or body.has_method("get_weapon_manager"):
		print("Player character detected, processing pickup...")

		# Defer the pickup processing to avoid physics state changes during physics callback
		_process_pickup.call_deferred(body)
	else:
		print("Non-player body entered pickup: ", body.get_class())

func _process_pickup(body):
	# Double-check availability to prevent multiple pickups
	if !is_available:
		return

	# Immediately disable to prevent multiple triggers
	is_available = false

	# Find the weapon manager
	var weapon_manager = _find_weapon_manager(body)

	if weapon_manager:
		var pickup_successful = await _give_weapon_to_manager(weapon_manager)

		if pickup_successful:
			_handle_successful_pickup(body)
		else:
			# Re-enable if pickup failed
			is_available = true
	else:
		print("No WeaponManager found!")
		# Re-enable if no weapon manager found
		is_available = true

func _find_weapon_manager(body):
	var weapon_manager = null

	# Try direct method
	if body.has_method("get_weapon_manager"):
		weapon_manager = body.get_weapon_manager()
		print("WeaponManager found via get_weapon_manager()")
		return weapon_manager

	# Try direct reference
	if body.has_node("WeaponManager"):
		weapon_manager = body.get_node("WeaponManager")
		print("WeaponManager found directly on player")
		return weapon_manager

	# Try to find in camera mount
	if body.has_node("CameraMount/Camera/WeaponManager"):
		weapon_manager = body.get_node("CameraMount/Camera/WeaponManager")
		print("WeaponManager found in camera mount")
		return weapon_manager

	# Try to find in children
	for child in body.get_children():
		if child.has_method("add_weapon_from_resource"):
			weapon_manager = child
			print("WeaponManager found in player's children")
			return weapon_manager

	# Try parent
	var parent = body.get_parent()
	if parent and parent.has_node("WeaponManager"):
		weapon_manager = parent.get_node("WeaponManager")
		print("WeaponManager found in player's parent")
		return weapon_manager

	return null

func _give_weapon_to_manager(weapon_manager) -> bool:
	var pickup_successful = false

	if weapon_resource != null:
		print("Adding weapon from resource: ", weapon_resource)
		# Give the weapon
		pickup_successful = await weapon_manager.add_weapon_from_resource(weapon_resource)
		print("Weapon added successfully: ", pickup_successful)

	if ammo_amount > 0:
		print("Adding ammo: ", ammo_amount, " for weapon type: ", weapon_type)
		# Give ammo for this weapon type
		var ammo_added = weapon_manager.add_ammo_to_weapon_type(weapon_type, ammo_amount)
		pickup_successful = pickup_successful or ammo_added > 0
		print("Ammo added: ", ammo_added)

	return pickup_successful

func _handle_successful_pickup(body):
	print("Pickup successful!")

	# Emit signal
	emit_signal("pickup_collected", body, weapon_type, ammo_amount)

	# Play pickup effect
	if pickup_effect:
		var effect = pickup_effect.instantiate()
		get_tree().get_root().add_child(effect)
		effect.global_transform = global_transform

	# Play pickup sound
	if pickup_sound and audio_player:
		audio_player.stream = pickup_sound
		audio_player.play()

	# Hide pickup visuals (is_available already set to false in _process_pickup)
	if pickup_mesh:
		pickup_mesh.visible = false
	if collision_shape:
		# Use call_deferred to avoid changing physics state during physics callback
		collision_shape.call_deferred("set_disabled", true)

	# Start respawn timer if needed
	if respawn_time > 0:
		# Create a timer that's not affected by pause
		var timer = Timer.new()
		timer.process_mode = Node.PROCESS_MODE_ALWAYS # Make sure it runs even when paused
		timer.wait_time = respawn_time
		timer.one_shot = true
		add_child(timer)
		timer.start()
		await timer.timeout
		timer.queue_free()
		respawn()

func respawn():
	# Re-enable pickup
	is_available = true
	if pickup_mesh:
		pickup_mesh.visible = true
	if collision_shape:
		# Use call_deferred to avoid changing physics state during physics callback
		collision_shape.call_deferred("set_disabled", false)

	print("Weapon pickup respawned: " + weapon_type)
