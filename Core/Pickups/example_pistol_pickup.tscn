[gd_scene load_steps=8 format=3 uid="uid://bqssmjgjpuuv5"]

[ext_resource type="Script" uid="uid://dfmf3x4jrvmto" path="res://Core/Pickups/WeaponPickup.gd" id="1_d1ikd"]
[ext_resource type="Script" uid="uid://d0mcbtnou8nw4" path="res://Core/Weapons/Weapon.gd" id="2_b7kls"]

[sub_resource type="BoxMesh" id="BoxMesh_xgp6i"]
size = Vector3(0.1, 0.1, 0.3)

[sub_resource type="PackedScene" id="PackedScene_0o8oo"]
_bundled = {
"conn_count": 0,
"conns": PackedInt32Array(),
"editable_instances": [],
"names": PackedStringArray("Example Pistol", "script", "weapon_name", "weapon_description", "damage", "fire_rate", "reload_time", "ammo_per_clip", "max_ammo", "spread", "Node3D", "MeshInstance3D", "mesh", "RayCast3D", "target_position", "MuzzlePosition", "transform", "AudioStreamPlayer3D", "AnimationPlayer"),
"node_count": 6,
"node_paths": [],
"nodes": PackedInt32Array(-1, -1, 10, 0, -1, 9, 1, 0, 2, 1, 3, 2, 4, 3, 5, 4, 6, 5, 7, 6, 8, 7, 9, 4, 0, 0, 0, 11, 11, -1, 1, 12, 8, 0, 0, 0, 13, 13, -1, 1, 14, 9, 0, 0, 0, 10, 15, -1, 1, 16, 10, 0, 0, 0, 17, 17, -1, 0, 0, 0, 0, 18, 18, -1, 0, 0),
"variants": [ExtResource("2_b7kls"), "Example Pistol", "A demonstration pistol created with the Weapon Manager", 25.0, 2.0, 1.0, 12, 60, SubResource("BoxMesh_xgp6i"), Vector3(0, 0, -500), Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -0.3)],
"version": 3
}

[sub_resource type="BoxShape3D" id="BoxShape3D_stdwb"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_o0mp6"]
albedo_color = Color(1, 0.647059, 0, 1)
emission_enabled = true
emission = Color(0.3, 0.194118, 0, 0.3)

[sub_resource type="BoxMesh" id="BoxMesh_p7v4f"]
size = Vector3(0.5, 0.3, 0.1)

[node name="Example Pistol_Pickup" type="Area3D"]
script = ExtResource("1_d1ikd")
weapon_resource = SubResource("PackedScene_0o8oo")
weapon_type = "Example Pistol"
ammo_amount = 12

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_stdwb")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
material_override = SubResource("StandardMaterial3D_o0mp6")
mesh = SubResource("BoxMesh_p7v4f")

[node name="AudioStreamPlayer3D" type="AudioStreamPlayer3D" parent="."]
