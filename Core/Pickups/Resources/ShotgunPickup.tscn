[gd_scene load_steps=5 format=3 uid="uid://f1yvxw3xdpnqr"]

[ext_resource type="Script" path="res://Core/Pickups/WeaponPickup.gd" id="1_pickup"]
[ext_resource type="PackedScene" uid="uid://d1yvxw3xdpnqr" path="res://Core/Weapons/Resources/Shotgun.tscn" id="2_shotgun"]

[sub_resource type="BoxShape3D" id="BoxShape3D_pickup"]
size = Vector3(0.5, 0.5, 0.8)

[sub_resource type="BoxMesh" id="BoxMesh_shotgun"]
size = Vector3(0.2, 0.2, 0.8)

[node name="ShotgunPickup" type="Area3D"]
collision_layer = 4
collision_mask = 2
script = ExtResource("1_pickup")
weapon_resource = ExtResource("2_shotgun")
weapon_type = "Shotgun"
ammo_amount = 16
rotate_speed = 1.5
bob_height = 0.3
bob_speed = 1.2

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_pickup")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
mesh = SubResource("BoxMesh_shotgun")

[node name="AudioStreamPlayer3D" type="AudioStreamPlayer3D" parent="."]
