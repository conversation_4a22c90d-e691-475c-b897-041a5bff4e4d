[gd_scene load_steps=5 format=3 uid="uid://bb55rtaf8bv78"]

[ext_resource type="Script" path="res://Core/Pickups/WeaponPickup.gd" id="1_pickup"]
[ext_resource type="PackedScene" uid="uid://mprs6sym7hqw" path="res://Core/Weapons/Resources/PP8.tscn" id="2_pp8"]

[sub_resource type="BoxShape3D" id="BoxShape3D_pickup"]
size = Vector3(0.4, 0.3, 0.4)

[sub_resource type="BoxMesh" id="BoxMesh_pp8"]
size = Vector3(0.25, 0.15, 0.4)

[node name="PP8Pickup" type="Area3D"]
collision_layer = 4
collision_mask = 2
script = ExtResource("1_pickup")
weapon_resource = ExtResource("2_pp8")
weapon_type = "PP8"
ammo_amount = 30
rotate_speed = 3.0
bob_height = 0.25
bob_speed = 2.0

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_pickup")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
mesh = SubResource("BoxMesh_pp8")

[node name="AudioStreamPlayer3D" type="AudioStreamPlayer3D" parent="."]
