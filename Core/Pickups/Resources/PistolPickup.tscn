[gd_scene load_steps=5 format=3 uid="uid://e1yvxw3xdpnqr"]

[ext_resource type="Script" path="res://Core/Pickups/WeaponPickup.gd" id="1_pickup"]
[ext_resource type="PackedScene" uid="uid://c1yvxw3xdpnqr" path="res://Core/Weapons/Resources/Pistol.tscn" id="2_pistol"]

[sub_resource type="BoxShape3D" id="BoxShape3D_pickup"]
size = Vector3(0.5, 0.5, 0.5)

[sub_resource type="BoxMesh" id="BoxMesh_pistol"]
size = Vector3(0.3, 0.2, 0.5)

[node name="PistolPickup" type="Area3D"]
collision_layer = 4
collision_mask = 2
script = ExtResource("1_pickup")
weapon_resource = ExtResource("2_pistol")
weapon_type = "Pistol"
ammo_amount = 24
rotate_speed = 2.0
bob_height = 0.3
bob_speed = 1.5

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_pickup")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
mesh = SubResource("BoxMesh_pistol")

[node name="AudioStreamPlayer3D" type="AudioStreamPlayer3D" parent="."]
