extends Node
class_name HealthSystem

@export var max_health: float = 100.0
@export var starting_health: float = 100.0
@export var armor: float = 0.0
@export var max_armor: float = 100.0
@export var armor_absorption: float = 0.5  # Percentage of damage absorbed by armor
@export var regeneration_enabled: bool = false
@export var regeneration_rate: float = 1.0  # Health per second
@export var regeneration_delay: float = 5.0  # Seconds after taking damage
@export var critical_health_threshold: float = 25.0

var current_health: float
var current_armor: float
var last_damage_time: float = 0.0
var time_since_damage: float = 0.0
var is_dead: bool = false

# Signals
signal health_changed(new_health, damage_taken)
signal armor_changed(new_armor)
signal critical_health(is_critical)
signal died
signal healed(amount)

func _ready():
    current_health = starting_health
    current_armor = armor
    
func _process(delta):
    if regeneration_enabled and !is_dead:
        time_since_damage += delta
        
        if time_since_damage >= regeneration_delay and current_health < max_health:
            heal(regeneration_rate * delta)

func take_damage(amount: float) -> float:
    if is_dead or amount <= 0:
        return 0.0
    
    var actual_damage = amount
    var armor_damage = 0.0
    
    # Apply armor reduction if we have armor
    if current_armor > 0:
        armor_damage = min(current_armor, amount * armor_absorption)
        current_armor -= armor_damage
        actual_damage -= armor_damage
        emit_signal("armor_changed", current_armor)
    
    # Apply remaining damage to health
    current_health -= actual_damage
    time_since_damage = 0.0
    last_damage_time = Time.get_ticks_msec() / 1000.0
    
    # Check for critical health
    if current_health <= critical_health_threshold and current_health > 0:
        emit_signal("critical_health", true)
    
    # Check for death
    if current_health <= 0 and !is_dead:
        current_health = 0
        is_dead = true
        emit_signal("died")
    
    emit_signal("health_changed", current_health, actual_damage)
    return actual_damage

func heal(amount: float) -> float:
    if is_dead or amount <= 0 or current_health >= max_health:
        return 0.0
    
    var before_health = current_health
    current_health = min(current_health + amount, max_health)
    var actual_heal = current_health - before_health
    
    # Check if we're no longer in critical health
    if before_health <= critical_health_threshold and current_health > critical_health_threshold:
        emit_signal("critical_health", false)
    
    emit_signal("health_changed", current_health, -actual_heal)
    emit_signal("healed", actual_heal)
    return actual_heal

func add_armor(amount: float) -> float:
    if amount <= 0 or current_armor >= max_armor:
        return 0.0
    
    var before_armor = current_armor
    current_armor = min(current_armor + amount, max_armor)
    var actual_armor_added = current_armor - before_armor
    
    emit_signal("armor_changed", current_armor)
    return actual_armor_added

func reset():
    current_health = starting_health
    current_armor = armor
    is_dead = false
    time_since_damage = 0.0
    last_damage_time = 0.0
    
    emit_signal("health_changed", current_health, 0)
    emit_signal("armor_changed", current_armor)
    
    if current_health <= critical_health_threshold:
        emit_signal("critical_health", true)
    else:
        emit_signal("critical_health", false)

func get_health_percent() -> float:
    return current_health / max_health

func get_armor_percent() -> float:
    return current_armor / max_armor if max_armor > 0 else 0.0
