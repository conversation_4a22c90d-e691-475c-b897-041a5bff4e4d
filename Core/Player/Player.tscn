[gd_scene load_steps=10 format=3 uid="uid://b3yxcv7jwaq8r"]

[ext_resource type="Script" uid="uid://bcn416ddnkl6o" path="res://Core/Player/PlayerController.gd" id="1_player"]
[ext_resource type="Script" uid="uid://c1pein18txais" path="res://Core/Player/HealthSystem.gd" id="2_health"]
[ext_resource type="Script" uid="uid://dtcxju0wtowha" path="res://Core/Weapons/WeaponManager.gd" id="3_weapons"]
[ext_resource type="Script" uid="uid://53cmsbmed6x0" path="res://Core/UI/WeaponHUD.gd" id="4_hud"]
[ext_resource type="Script" uid="uid://c10yc2ysnq05x" path="res://Core/UI/CrosshairUI.gd" id="5_crosshair"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_player"]
height = 1.8

[sub_resource type="CapsuleMesh" id="CapsuleMesh_player"]
height = 1.8

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_player"]
albedo_color = Color(0.2, 0.6, 1, 1)

[sub_resource type="BoxMesh" id="BoxMesh_weapon"]
size = Vector3(0.1, 0.1, 0.3)

[node name="Player" type="CharacterBody3D" groups=["player"]]
collision_layer = 2
collision_mask = 5
script = ExtResource("1_player")

[node name="CollisionShape" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
shape = SubResource("CapsuleShape3D_player")

[node name="PlayerMesh" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
visible = false
mesh = SubResource("CapsuleMesh_player")
surface_material_override/0 = SubResource("StandardMaterial3D_player")

[node name="CameraMount" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.9, 0)

[node name="Camera" type="Camera3D" parent="CameraMount"]
current = true

[node name="WeaponManager" type="Node3D" parent="CameraMount/Camera"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.3, -0.3, -0.7)
script = ExtResource("3_weapons")

[node name="WeaponPosition" type="Node3D" parent="CameraMount/Camera/WeaponManager"]
transform = Transform3D(0.966, 0, -0.259, 0, 1, 0, 0.259, 0, 0.966, 0, 0, 0)

[node name="TestWeapon" type="MeshInstance3D" parent="CameraMount/Camera/WeaponManager/WeaponPosition"]
visible = false
mesh = SubResource("BoxMesh_weapon")

[node name="HealthSystem" type="Node" parent="."]
script = ExtResource("2_health")

[node name="FootstepAudio" type="AudioStreamPlayer3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.1, 0)

[node name="JumpAudio" type="AudioStreamPlayer3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.1, 0)

[node name="WeaponHUD" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("4_hud")
weapon_manager = NodePath("../CameraMount/Camera/WeaponManager")

[node name="WeaponNameLabel" type="Label" parent="WeaponHUD"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -80.0
offset_right = 300.0
offset_bottom = -50.0
grow_vertical = 0
theme_override_font_sizes/font_size = 24
text = "No Weapon"

[node name="AmmoLabel" type="Label" parent="WeaponHUD"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -50.0
offset_right = 300.0
offset_bottom = -20.0
grow_vertical = 0
theme_override_font_sizes/font_size = 24
text = "0 / 0"

[node name="SpeedLabel" type="Label" parent="WeaponHUD"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -200.0
offset_top = -50.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 0
grow_vertical = 0
theme_override_font_sizes/font_size = 18
text = "Speed: 0.0 m/s"
horizontal_alignment = 2

[node name="WeaponIcon" type="TextureRect" parent="WeaponHUD"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -100.0
offset_top = -100.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 0
grow_vertical = 0
expand_mode = 1
stretch_mode = 5

[node name="CrosshairUI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("5_crosshair")
crosshair_size = 10.0
