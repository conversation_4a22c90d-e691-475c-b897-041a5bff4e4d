extends Area3D
class_name JumpPad

@export var jump_force: float = 20.0
@export var jump_sound: AudioStream
@export var cooldown_time: float = 1.0  # Cooldown in seconds

@onready var audio_player: AudioStreamPlayer3D = $AudioStreamPlayer3D if has_node("AudioStreamPlayer3D") else null

var bodies_in_cooldown = {}

func _ready():
    body_entered.connect(_on_body_entered)
    body_exited.connect(_on_body_exited)

    # Set up collision
    collision_layer = 1  # World layer
    collision_mask = 2   # Player layer

    print("JumpPad initialized with force: ", jump_force)

func _on_body_entered(body):
    if body is CharacterBody3D and not bodies_in_cooldown.has(body):
        print("Player entered jump pad")

        # Apply jump force
        body.velocity.y = jump_force

        # Add to cooldown
        bodies_in_cooldown[body] = true

        # Start cooldown timer
        var timer = get_tree().create_timer(cooldown_time)
        timer.timeout.connect(func(): _remove_from_cooldown(body))

        # Play sound
        if jump_sound and audio_player:
            audio_player.stream = jump_sound
            audio_player.play()

func _on_body_exited(body):
    # Remove from cooldown when body exits
    if bodies_in_cooldown.has(body):
        bodies_in_cooldown.erase(body)

func _remove_from_cooldown(body):
    if bodies_in_cooldown.has(body):
        bodies_in_cooldown.erase(body)
