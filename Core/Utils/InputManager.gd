extends Node
# No class_name to avoid conflict with autoload

# This singleton manages input capture and handling

# Signals
signal mouse_mode_changed(mode)
signal pause_toggled(is_paused)

# State
var is_game_paused: bool = false
var mouse_captured: bool = false
var pause_input_handled: bool = false

func _ready():
    # Initialize
    capture_mouse()
    print("InputManager initialized")

func _process(_delta):
    # Reset pause input handled flag each frame
    pause_input_handled = false

    # Debug check for Esc key
    if Input.is_action_just_pressed("pauseMenu"):
        print("InputManager: Detected pauseMenu action (Esc key)")

# We'll let PauseMenu handle the input instead
# This avoids double-handling the pause action
# func _input(event):
#     # Handle pause menu toggle
#     if event.is_action_pressed("pauseMenu"):
#         toggle_pause()

func capture_mouse():
    Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
    mouse_captured = true
    emit_signal("mouse_mode_changed", Input.MOUSE_MODE_CAPTURED)
    print("Mouse captured")

func release_mouse():
    Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
    mouse_captured = false
    emit_signal("mouse_mode_changed", Input.MOUSE_MODE_VISIBLE)
    print("Mouse released")

func toggle_mouse_capture():
    if mouse_captured:
        release_mouse()
    else:
        capture_mouse()

func toggle_pause():
    is_game_paused = !is_game_paused

    print("InputManager: Toggling pause state to: ", is_game_paused)

    if is_game_paused:
        # Use get_tree().paused instead of Engine.time_scale
        get_tree().paused = true
        release_mouse()
    else:
        # Use get_tree().paused instead of Engine.time_scale
        get_tree().paused = false
        capture_mouse()

    # Emit signal after state changes are complete
    emit_signal("pause_toggled", is_game_paused)
    print("Game paused: ", is_game_paused)

func is_paused() -> bool:
    return is_game_paused

func is_mouse_captured() -> bool:
    return mouse_captured
