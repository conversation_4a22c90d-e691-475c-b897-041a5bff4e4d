extends Node

## Weapon Database System
## Manages persistent weapon and ammo data across the game session
## Tracks owned weapons, current ammo, reserve ammo, and weapon states

# Weapon data structure
class WeaponData:
	var weapon_name: String
	var current_ammo: int
	var reserve_ammo: int
	var max_ammo: int
	var ammo_per_clip: int
	var is_owned: bool = false
	var is_dual_wield: bool = false
	var left_clip_ammo: int = 0  # For dual-wield weapons
	var right_clip_ammo: int = 0  # For dual-wield weapons
	
	func _init(name: String = "", current: int = 0, reserve: int = 0, max_ammo_val: int = 100, per_clip: int = 10):
		weapon_name = name
		current_ammo = current
		reserve_ammo = reserve
		max_ammo = max_ammo_val
		ammo_per_clip = per_clip

# Database storage
var weapon_data: Dictionary = {}

# Signals
signal weapon_data_changed(weapon_name: String, data: WeaponData)
signal ammo_updated(weapon_name: String, current: int, reserve: int)

func _ready():
	print("WeaponDatabase: Initialized")

## Register a new weapon type in the database
func register_weapon(weapon_name: String, max_ammo: int, ammo_per_clip: int) -> WeaponData:
	if weapon_name in weapon_data:
		print("WeaponDatabase: Weapon " + weapon_name + " already registered")
		return weapon_data[weapon_name]
	
	var data = WeaponData.new(weapon_name, 0, 0, max_ammo, ammo_per_clip)
	weapon_data[weapon_name] = data
	print("WeaponDatabase: Registered weapon " + weapon_name)
	return data

## Add a weapon to the player's inventory
func add_weapon(weapon_name: String, current_ammo: int = 0, reserve_ammo: int = 0, max_ammo: int = 100, ammo_per_clip: int = 10) -> WeaponData:
	var data: WeaponData
	
	if weapon_name in weapon_data:
		data = weapon_data[weapon_name]
		if not data.is_owned:
			# First time picking up this weapon
			data.is_owned = true
			data.current_ammo = current_ammo
			data.reserve_ammo = reserve_ammo
			print("WeaponDatabase: Added weapon " + weapon_name + " to inventory")
		else:
			# Already own this weapon, add ammo
			add_ammo(weapon_name, current_ammo + reserve_ammo)
			print("WeaponDatabase: Added ammo to existing weapon " + weapon_name)
	else:
		# Register and add new weapon
		data = WeaponData.new(weapon_name, current_ammo, reserve_ammo, max_ammo, ammo_per_clip)
		data.is_owned = true
		weapon_data[weapon_name] = data
		print("WeaponDatabase: Registered and added new weapon " + weapon_name)
	
	emit_signal("weapon_data_changed", weapon_name, data)
	return data

## Add ammo to a weapon
func add_ammo(weapon_name: String, amount: int) -> int:
	if not weapon_name in weapon_data:
		print("WeaponDatabase: Cannot add ammo to unregistered weapon " + weapon_name)
		return 0
	
	var data = weapon_data[weapon_name]
	if not data.is_owned:
		print("WeaponDatabase: Cannot add ammo to unowned weapon " + weapon_name)
		return 0
	
	# Calculate current total and max capacity
	var current_total = data.current_ammo + data.reserve_ammo
	if data.is_dual_wield:
		current_total = data.left_clip_ammo + data.right_clip_ammo + data.reserve_ammo
	
	var can_add = max(0, data.max_ammo - current_total)
	var actual_amount = min(amount, can_add)
	
	if actual_amount <= 0:
		print("WeaponDatabase: " + weapon_name + " already at max ammo capacity")
		return 0
	
	data.reserve_ammo += actual_amount
	print("WeaponDatabase: Added " + str(actual_amount) + " ammo to " + weapon_name)
	
	emit_signal("weapon_data_changed", weapon_name, data)
	emit_signal("ammo_updated", weapon_name, data.current_ammo, data.reserve_ammo)
	return actual_amount

## Use ammo from a weapon (when firing)
func use_ammo(weapon_name: String, amount: int = 1) -> bool:
	if not weapon_name in weapon_data:
		return false
	
	var data = weapon_data[weapon_name]
	if not data.is_owned or data.current_ammo < amount:
		return false
	
	data.current_ammo -= amount
	emit_signal("weapon_data_changed", weapon_name, data)
	emit_signal("ammo_updated", weapon_name, data.current_ammo, data.reserve_ammo)
	return true

## Reload a weapon
func reload_weapon(weapon_name: String) -> bool:
	if not weapon_name in weapon_data:
		return false
	
	var data = weapon_data[weapon_name]
	if not data.is_owned:
		return false
	
	if data.current_ammo >= data.ammo_per_clip or data.reserve_ammo <= 0:
		return false
	
	var ammo_needed = data.ammo_per_clip - data.current_ammo
	var ammo_to_reload = min(ammo_needed, data.reserve_ammo)
	
	data.current_ammo += ammo_to_reload
	data.reserve_ammo -= ammo_to_reload
	
	print("WeaponDatabase: Reloaded " + weapon_name + ". Current: " + str(data.current_ammo) + " Reserve: " + str(data.reserve_ammo))
	
	emit_signal("weapon_data_changed", weapon_name, data)
	emit_signal("ammo_updated", weapon_name, data.current_ammo, data.reserve_ammo)
	return true

## Setup dual-wield for PP8
func setup_dual_wield(weapon_name: String, left_ammo: int, right_ammo: int, reserve: int) -> bool:
	if not weapon_name in weapon_data:
		return false
	
	var data = weapon_data[weapon_name]
	data.is_dual_wield = true
	data.left_clip_ammo = left_ammo
	data.right_clip_ammo = right_ammo
	data.reserve_ammo = reserve
	data.current_ammo = right_ammo  # Display right gun's ammo
	
	print("WeaponDatabase: Setup dual-wield for " + weapon_name)
	emit_signal("weapon_data_changed", weapon_name, data)
	return true

## Get weapon data
func get_weapon_data(weapon_name: String) -> WeaponData:
	if weapon_name in weapon_data:
		return weapon_data[weapon_name]
	return null

## Check if player owns a weapon
func has_weapon(weapon_name: String) -> bool:
	if weapon_name in weapon_data:
		return weapon_data[weapon_name].is_owned
	return false

## Get all owned weapons
func get_owned_weapons() -> Array:
	var owned: Array = []
	for weapon_name in weapon_data:
		if weapon_data[weapon_name].is_owned:
			owned.append(weapon_name)
	return owned

## Update weapon data from weapon instance
func sync_from_weapon(weapon) -> bool:
	if not weapon or not "weapon_name" in weapon:
		return false
	
	var weapon_name = weapon.weapon_name
	var data = get_weapon_data(weapon_name)
	if not data:
		return false
	
	# Update database from weapon
	if weapon.has_method("get_ammo_info"):
		var ammo_info = weapon.get_ammo_info()
		data.current_ammo = ammo_info.get("current", 0)
		data.reserve_ammo = ammo_info.get("reserve", 0)
		
		if ammo_info.has("dual_wield") and ammo_info.dual_wield:
			data.is_dual_wield = true
			data.left_clip_ammo = ammo_info.get("left_clip", 0)
			data.right_clip_ammo = ammo_info.get("right_clip", 0)
	
	emit_signal("weapon_data_changed", weapon_name, data)
	return true

## Update weapon instance from database
func sync_to_weapon(weapon) -> bool:
	if not weapon or not "weapon_name" in weapon:
		return false
	
	var weapon_name = weapon.weapon_name
	var data = get_weapon_data(weapon_name)
	if not data:
		return false
	
	# Update weapon from database
	weapon.current_ammo = data.current_ammo
	weapon.reserve_ammo = data.reserve_ammo
	
	if data.is_dual_wield and weapon.has_method("setup_dual_wield"):
		weapon.left_clip_ammo = data.left_clip_ammo
		weapon.right_clip_ammo = data.right_clip_ammo
		weapon.dual_wield_ammo = data.reserve_ammo
	
	return true
