[gd_resource type="Resource" load_steps=2 format=3]

[ext_resource type="Script" path="res://addons/weapon_manager/weapon_resource.gd" id="1"]

[resource]
script = ExtResource("1")
weapon_name = "Example Pistol"
weapon_description = "A demonstration pistol created with the Weapon Manager"
weapon_icon = null
weapon_model = null
damage = 25.0
fire_rate = 2.0
reload_time = 1.0
ammo_per_clip = 12
max_ammo = 60
auto_fire = false
spread = 2.0
weapon_type = "Pistol"
is_dual_wieldable = true
projectile_type = "hitscan"
fire_sound = null
reload_sound = null
empty_sound = null
equip_sound = null
muzzle_flash = null
impact_effect = null
shell_casing = null
fire_animation = "fire"
reload_animation = "reload"
equip_animation = "equip"
idle_animation = "idle"
recoil_strength = 0.5
range_max = 500.0
penetration = 0
knockback_force = 0.0
created_date = "2025-08-27T12:00:00"
last_modified = "2025-08-27T12:00:00"
template_used = "Basic Pistol"
