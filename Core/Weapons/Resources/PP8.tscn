[gd_scene load_steps=6 format=3 uid="uid://mprs6sym7hqw"]

[ext_resource type="Script" path="res://Core/Weapons/PP8Weapon.gd" id="1_pp8"]

[sub_resource type="BoxMesh" id="BoxMesh_body"]
size = Vector3(0.08, 0.08, 0.25)

[sub_resource type="BoxMesh" id="BoxMesh_barrel"]
size = Vector3(0.04, 0.04, 0.15)

[sub_resource type="Animation" id="Animation_fire"]
resource_name = "fire"
length = 0.15
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.05, 0.15),
"transitions": PackedFloat32Array(0.5, 2, 1),
"update": 0,
"values": [Vector3(0, 0, 0), Vector3(0.15, 0, 0), Vector3(0, 0, 0)]
}

[sub_resource type="Animation" id="Animation_reload"]
resource_name = "reload"
length = 1.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3, 0.9, 1.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector3(0, 0, 0), Vector3(0, 0.1, 0), Vector3(0, 0.1, 0), Vector3(0, 0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.3, 0.9, 1.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector3(0, 0, 0), Vector3(0.1, 0, 0), Vector3(0.1, 0, 0), Vector3(0, 0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_pp8"]
_data = {
"fire": SubResource("Animation_fire"),
"reload": SubResource("Animation_reload")
}

[node name="PP8" type="Node3D"]
script = ExtResource("1_pp8")
weapon_name = "PP8"
weapon_description = "A compact dual-wield pistol"
damage = 20.0
fire_rate = 3.0
reload_time = 1.2
ammo_per_clip = 15
max_ammo = 90
spread = 1.5

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
mesh = SubResource("BoxMesh_body")

[node name="Barrel" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.02, -0.15)
mesh = SubResource("BoxMesh_barrel")

[node name="MuzzlePosition" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.02, -0.25)

[node name="RayCast3D" type="RayCast3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.02, -0.25)
target_position = Vector3(0, 0, -100)
collision_mask = 1

[node name="AudioStreamPlayer3D" type="AudioStreamPlayer3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -0.25)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_pp8")
}
