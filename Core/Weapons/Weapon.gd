extends Node3D
class_name Weapon

# Weapon Properties
@export_group("Weapon Info")
@export var weapon_name: String = "Base Weapon"
@export var weapon_description: String = "Base weapon description"
@export var weapon_icon: Texture2D
@export var weapon_model: PackedScene

@export_group("Weapon Stats")
@export var damage: float = 10.0
@export var fire_rate: float = 1.0  # Shots per second
@export var reload_time: float = 1.5  # Seconds
@export var ammo_per_clip: int = 10
@export var max_ammo: int = 100
@export var auto_fire: bool = false
@export var spread: float = 0.0  # Bullet spread in degrees

@export_group("Weapon Effects")
@export var muzzle_flash: PackedScene
@export var impact_effect: PackedScene
@export var fire_sound: AudioStream
@export var reload_sound: AudioStream
@export var empty_sound: AudioStream
@export var equip_sound: AudioStream

@export_group("Weapon Animation")
@export var fire_animation: String = "fire"
@export var reload_animation: String = "reload"
@export var equip_animation: String = "equip"

# Weapon State
var current_ammo: int = 0
var reserve_ammo: int = 0
var is_reloading: bool = false
var can_fire: bool = true
var is_equipped: bool = false
var fire_cooldown: float = 0.0
var use_animation_fallbacks: bool = false # Flag to skip animations and use fallbacks

# References
@onready var animation_player: AnimationPlayer = $AnimationPlayer if has_node("AnimationPlayer") else null
@onready var audio_player: AudioStreamPlayer3D = $AudioStreamPlayer3D if has_node("AudioStreamPlayer3D") else null
@onready var raycast: RayCast3D = $RayCast3D if has_node("RayCast3D") else null
@onready var muzzle_position: Node3D = $MuzzlePosition if has_node("MuzzlePosition") else null
@onready var weapon_mesh: MeshInstance3D = $MeshInstance3D if has_node("MeshInstance3D") else null

# Signals
signal weapon_fired(weapon)
signal weapon_reloaded(weapon)
signal weapon_equipped(weapon)
signal weapon_unequipped(weapon)
signal ammo_changed(current, reserve)
signal hit_target(target, damage, hit_position, hit_normal)
signal no_ammo(weapon)

func _ready():
	# Initialize weapon
	current_ammo = ammo_per_clip
	reserve_ammo = max_ammo - current_ammo

	# Set process mode to ensure weapon works during pause
	process_mode = Node.PROCESS_MODE_PAUSABLE

	# Debug output
	print(weapon_name + " initialized")
	print("Required nodes check:")
	print("- AnimationPlayer: ", animation_player != null)
	print("- AudioStreamPlayer3D: ", audio_player != null)
	print("- RayCast3D: ", raycast != null)
	print("- MuzzlePosition: ", muzzle_position != null)

	# Check and initialize animation player
	if animation_player:
		# Check if animation library exists and create it if needed
		var has_libraries = false
		var library_list = animation_player.get_animation_library_list()
		if library_list != null and library_list.size() > 0:
			has_libraries = true

		# Only create a default library if none exist
		if not has_libraries:
			print("WARNING: Creating default animation library for " + weapon_name)
			var new_lib = AnimationLibrary.new()
			# Add a dummy animation to ensure the library is valid
			var dummy_anim = Animation.new()
			dummy_anim.length = 0.1  # Set a minimal length
			new_lib.add_animation("dummy", dummy_anim)

			# Use a try-catch to handle potential errors
			var result = animation_player.add_animation_library("default", new_lib)
			if result == OK:
				print("Created default animation library for " + weapon_name)
			else:
				print("WARNING: Failed to add animation library for " + weapon_name + ", error code: ", result)

		# Check for required animations
		var missing_animations = []
		var has_fire_anim = false
		var has_reload_anim = false
		var has_equip_anim = false

		# Check all libraries for the animations
		var init_library_list = animation_player.get_animation_library_list()
		if init_library_list != null:
			for lib_name in init_library_list:
				var lib = animation_player.get_animation_library(lib_name)
				if lib != null:
					if lib.has_animation(fire_animation):
						has_fire_anim = true
					if lib.has_animation(reload_animation):
						has_reload_anim = true
					if lib.has_animation(equip_animation):
						has_equip_anim = true

		if not has_fire_anim:
			missing_animations.append(fire_animation)
		if not has_reload_anim:
			missing_animations.append(reload_animation)
		if not has_equip_anim:
			missing_animations.append(equip_animation)

		if missing_animations.size() > 0:
			print("WARNING: " + weapon_name + " is missing animations: " + str(missing_animations))
	else:
		print("WARNING: " + weapon_name + " has no AnimationPlayer, will use fallback behavior")

	# Set up raycast
	if raycast:
		raycast.enabled = true
		raycast.collision_mask = 1  # Adjust based on your collision layers
	else:
		print("WARNING: " + weapon_name + " has no RayCast3D, hit detection will not work")

func _process(delta):
	# Handle fire cooldown
	if fire_cooldown > 0:
		fire_cooldown -= delta
		if fire_cooldown <= 0:
			can_fire = true

func equip():
	is_equipped = true
	visible = true

	# Play equip sound
	play_sound(equip_sound)

	# Play equip animation
	if animation_player and !use_animation_fallbacks:
		# Only try to play animations if we're not using fallbacks
		print("INFO: " + weapon_name + " - Attempting to play equip animation")

		# Try to play the animation, but catch any errors
		var played_successfully = false

		# Check if we have any animation libraries
		var has_libraries = false
		var equip_library_list = animation_player.get_animation_library_list()
		if equip_library_list != null and equip_library_list.size() > 0:
			has_libraries = true

		if has_libraries:
			# Try to find the animation in any library
			for lib_name in equip_library_list:
				var lib = animation_player.get_animation_library(lib_name)
				if lib != null and lib.has_animation(equip_animation):
					# Try to play the animation
					animation_player.play(equip_animation)
					played_successfully = true
					break

			if !played_successfully:
				print("WARNING: " + weapon_name + " - Equip animation not found, using fallback")
		else:
			print("WARNING: " + weapon_name + " - No animation libraries found, using fallback")

	emit_signal("weapon_equipped", self)
	emit_signal("ammo_changed", current_ammo, reserve_ammo)

func unequip():
	is_equipped = false
	visible = false
	emit_signal("weapon_unequipped", self)

func primary_fire():
	if !can_fire or is_reloading:
		return false

	if current_ammo <= 0:
		# No ammo available - play empty sound and trigger auto-reload
		play_sound(empty_sound)
		if reserve_ammo > 0:
			# Start automatic reload with animation
			print(weapon_name + ": Clip empty - starting automatic reload")
			auto_reload()
		else:
			emit_signal("no_ammo", self)
		return false

	_fire()
	return true

func _fire():
	# Reduce ammo
	current_ammo -= 1
	emit_signal("ammo_changed", current_ammo, reserve_ammo)

	# Set cooldown
	can_fire = false
	fire_cooldown = 1.0 / fire_rate

	# Play effects
	var animation_played = false

	# Only try to play animations if we're not using fallbacks
	if animation_player and !use_animation_fallbacks:
		print("INFO: " + weapon_name + " - Attempting to play fire animation")

		# Try to play the animation, but catch any errors
		var played_successfully = false

		# Check if we have any animation libraries
		var has_libraries = false
		var fire_library_list = animation_player.get_animation_library_list()
		if fire_library_list != null and fire_library_list.size() > 0:
			has_libraries = true

		if has_libraries:
			# Try to find the animation in any library
			for lib_name in fire_library_list:
				var lib = animation_player.get_animation_library(lib_name)
				if lib != null and lib.has_animation(fire_animation):
					# Try to play the animation
					animation_player.play(fire_animation)
					animation_played = true
					played_successfully = true
					break

			if !played_successfully:
				print("WARNING: " + weapon_name + " - Fire animation not found, using fallback")
		else:
			print("WARNING: " + weapon_name + " - No animation libraries found, using fallback")

	if !animation_played:
		print("INFO: " + weapon_name + " - Using fallback for fire animation")

	# Play sound
	play_sound(fire_sound)

	# Spawn muzzle flash
	if muzzle_flash:
		if muzzle_position:
			var flash_instance = muzzle_flash.instantiate()
			muzzle_position.add_child(flash_instance)
			# Auto-remove after a short time
			var timer = Timer.new()
			timer.process_mode = Node.PROCESS_MODE_ALWAYS # Make sure it runs even when paused
			timer.wait_time = 0.1
			timer.one_shot = true
			add_child(timer)
			timer.start()
			await timer.timeout
			timer.queue_free()
			if flash_instance and is_instance_valid(flash_instance):
				flash_instance.queue_free()
		else:
			print("WARNING: " + weapon_name + " - Cannot spawn muzzle flash - missing MuzzlePosition node")

	# Handle hit detection
	_handle_hit_detection()

	emit_signal("weapon_fired", self)

func _handle_hit_detection():
	# Base implementation using raycast
	if raycast and raycast.is_colliding():
		var target = raycast.get_collider()
		var hit_position = raycast.get_collision_point()
		var hit_normal = raycast.get_collision_normal()

		print(weapon_name + ": Hit detected at " + str(hit_position))

		# Spawn impact effect
		if impact_effect:
			var impact = impact_effect.instantiate()
			get_tree().get_root().add_child(impact)
			impact.global_transform.origin = hit_position

			# Orient the impact effect to face the normal
			var y = hit_normal
			var x = y.cross(Vector3.UP)
			if x.length_squared() < 0.001:
				x = y.cross(Vector3.RIGHT)
			var z = x.cross(y)
			impact.global_transform.basis = Basis(x.normalized(), y.normalized(), z.normalized())

			# Auto-remove after a short time
			var timer = Timer.new()
			timer.process_mode = Node.PROCESS_MODE_ALWAYS # Make sure it runs even when paused
			timer.wait_time = 2.0
			timer.one_shot = true
			add_child(timer)
			timer.start()
			await timer.timeout
			timer.queue_free()
			if impact and is_instance_valid(impact):
				impact.queue_free()

		# Apply damage if target has health
		if target.has_method("take_damage"):
			target.take_damage(damage, hit_position, hit_normal)
			print(weapon_name + ": Applied " + str(damage) + " damage to " + str(target))

		emit_signal("hit_target", target, damage, hit_position, hit_normal)
	elif !raycast:
		print(weapon_name + ": Cannot detect hits - missing RayCast3D node")
	else:
		print(weapon_name + ": Shot fired but no hit detected")

func reload():
	if is_reloading:
		print("INFO: " + weapon_name + " - Already reloading")
		return

	if current_ammo >= ammo_per_clip:
		print("INFO: " + weapon_name + " - Magazine already full")
		return

	if reserve_ammo <= 0:
		print("INFO: " + weapon_name + " - No reserve ammo available")
		return

	_perform_reload(true)

func auto_reload():
	if is_reloading:
		return

	if reserve_ammo <= 0:
		return

	_perform_reload(false)

func _perform_reload(is_manual: bool):
	is_reloading = true
	var reload_type = "Manual" if is_manual else "Automatic"
	print("INFO: " + weapon_name + " - " + reload_type + " reload started")

	if is_manual:
		play_sound(reload_sound)

	# Play reload animation with gun raising
	await _play_reload_animation()

	# Reload the full clip
	var ammo_needed = ammo_per_clip - current_ammo
	var ammo_to_reload = min(ammo_needed, reserve_ammo)

	current_ammo += ammo_to_reload
	reserve_ammo -= ammo_to_reload

	print("INFO: " + weapon_name + " - " + reload_type + " reload complete. Current: " + str(current_ammo) + " Reserve: " + str(reserve_ammo))

	is_reloading = false
	emit_signal("weapon_reloaded", self)
	emit_signal("ammo_changed", current_ammo, reserve_ammo)

func _play_reload_animation():
	var animation_played = false

	# Only try to play animations if we're not using fallbacks
	if animation_player and !use_animation_fallbacks:
		# Check if we have any animation libraries
		var has_libraries = false
		var reload_library_list = animation_player.get_animation_library_list()
		if reload_library_list != null and reload_library_list.size() > 0:
			has_libraries = true

		if has_libraries:
			# Try to find the reload animation in any library
			if reload_library_list != null:
				for lib_name in reload_library_list:
					var lib = animation_player.get_animation_library(lib_name)
					if lib != null and lib.has_animation(reload_animation):
						# Play the reload animation
						animation_player.play(reload_animation)
						animation_played = true
						# Wait for animation to finish
						await animation_player.animation_finished
						break

	if !animation_played:
		# Create custom reload animation with gun raising
		await _create_reload_animation()

func _create_reload_animation():
	var original_position = position
	var original_rotation = rotation
	var raise_height = 0.1
	var reload_duration = reload_time

	# Create tween for smooth animation
	var tween = create_tween()
	tween.set_parallel(true)

	# Raise gun up and rotate slightly
	tween.tween_property(self, "position", original_position + Vector3(0, raise_height, 0), reload_duration * 0.3)
	tween.tween_property(self, "rotation", original_rotation + Vector3(0.1, 0, 0), reload_duration * 0.3)

	# Wait for middle of reload
	await tween.tween_delay(reload_duration * 0.4).finished

	# Lower gun back to original position
	tween.tween_property(self, "position", original_position, reload_duration * 0.3)
	tween.tween_property(self, "rotation", original_rotation, reload_duration * 0.3)

	# Wait for animation to complete
	await tween.finished

func add_ammo(amount: int) -> int:
	if amount <= 0:
		return 0

	# Calculate current total ammo and max capacity
	var current_total = current_ammo + reserve_ammo
	var can_add = max(0, max_ammo - current_total)
	var actual_amount = min(amount, can_add)

	if actual_amount <= 0:
		print(weapon_name + ": Already at max ammo capacity (" + str(max_ammo) + ")")
		return 0

	reserve_ammo += actual_amount
	emit_signal("ammo_changed", current_ammo, reserve_ammo)
	print(weapon_name + ": Added " + str(actual_amount) + " ammo. Current: " + str(current_ammo) + " Reserve: " + str(reserve_ammo))
	return actual_amount  # Return amount actually added

func play_sound(sound: AudioStream):
	if sound:
		if audio_player:
			audio_player.stream = sound
			audio_player.play()
			return true
		else:
			print("ERROR: " + weapon_name + " - Cannot play sound - missing AudioStreamPlayer3D node")
			return false
	else:
		if audio_player:
			print("WARNING: " + weapon_name + " - Cannot play sound - missing sound resource")
		else:
			print("ERROR: " + weapon_name + " - Cannot play sound - missing both sound resource and AudioStreamPlayer3D")
		return false

func get_ammo_info() -> Dictionary:
	return {
		"current": current_ammo,
		"reserve": reserve_ammo,
		"max": max_ammo,
		"per_clip": ammo_per_clip
	}

func has_ammo() -> bool:
	return current_ammo > 0 or reserve_ammo > 0

# Virtual method to be overridden by specific weapons
func secondary_fire():
	# Default implementation does nothing
	return false
