extends Weapon
class_name PP8Weapon

# Dual-wield properties
@export var is_dual_wielding: bool = false
@export var is_left_hand: bool = false  # Whether this is the left-hand gun
@export var dual_wield_partner: PP8Weapon = null  # Reference to the other gun
var alternate_fire: bool = false  # Which gun fires next

# Dual-wield state
var dual_wield_ammo: int = 0  # Shared reserve ammo pool
var left_clip_ammo: int = 0   # Left gun's current clip
var right_clip_ammo: int = 0  # Right gun's current clip

func _ready():
	super._ready()
	print("PP8 initialized - Dual-wield capable: ", weapon_name)

func setup_dual_wield():
	"""This method is called by WeaponManager to enable dual-wield mode"""
	if is_dual_wielding:
		return  # Already dual wielding

	print("PP8: Setting up dual-wield mode")
	is_dual_wielding = true

	# Initialize separate clips for dual-wield
	if is_left_hand:
		left_clip_ammo = current_ammo
	else:
		right_clip_ammo = current_ammo

	print("PP8: Dual-wield setup complete. Left clip: ", left_clip_ammo, " Right clip: ", right_clip_ammo, " Reserve: ", dual_wield_ammo)

func add_ammo(amount: int) -> int:
	if is_dual_wielding:
		# Only the right-hand gun should handle ammo distribution
		if is_left_hand:
			return 0

		# Calculate current total ammo
		var current_total = left_clip_ammo + right_clip_ammo + dual_wield_ammo
		var max_total = max_ammo * 2  # Double max for dual wield

		# Don't add more than max capacity
		var can_add = max(0, max_total - current_total)
		var actual_amount = min(amount, can_add)

		if actual_amount <= 0:
			print("PP8: Already at max ammo capacity")
			return 0

		# Add to shared reserve pool first
		dual_wield_ammo += actual_amount

		# Fill clips if they're not full
		var left_needed = max(0, ammo_per_clip - left_clip_ammo)
		var right_needed = max(0, ammo_per_clip - right_clip_ammo)

		# Fill left clip first
		if left_needed > 0 and dual_wield_ammo > 0:
			var left_fill = min(left_needed, dual_wield_ammo)
			left_clip_ammo += left_fill
			dual_wield_ammo -= left_fill

		# Fill right clip second
		if right_needed > 0 and dual_wield_ammo > 0:
			var right_fill = min(right_needed, dual_wield_ammo)
			right_clip_ammo += right_fill
			dual_wield_ammo -= right_fill

		# Update display values
		current_ammo = right_clip_ammo
		reserve_ammo = dual_wield_ammo

		# Update partner
		if dual_wield_partner:
			dual_wield_partner.left_clip_ammo = left_clip_ammo
			dual_wield_partner.right_clip_ammo = right_clip_ammo
			dual_wield_partner.dual_wield_ammo = dual_wield_ammo
			dual_wield_partner.current_ammo = left_clip_ammo
			dual_wield_partner.reserve_ammo = dual_wield_ammo

		emit_signal("ammo_changed", current_ammo, reserve_ammo)
		print("PP8: Added " + str(actual_amount) + " ammo. Left: " + str(left_clip_ammo) + " Right: " + str(right_clip_ammo) + " Reserve: " + str(dual_wield_ammo))
		return actual_amount
	else:
		# Single weapon - respect max ammo limit
		var current_total = current_ammo + reserve_ammo
		var can_add = max(0, max_ammo - current_total)
		var actual_amount = min(amount, can_add)

		if actual_amount <= 0:
			print(weapon_name + ": Already at max ammo capacity")
			return 0

		return super.add_ammo(actual_amount)

func primary_fire():
	if !can_fire or is_reloading:
		return false

	if is_dual_wielding:
		_dual_wield_fire()
	else:
		# Single weapon behavior - use base class logic
		if current_ammo <= 0:
			# No ammo available - play empty sound and trigger auto-reload
			play_sound(empty_sound)
			if reserve_ammo > 0:
				# Start automatic reload with animation
				print(weapon_name + ": Clip empty - starting automatic reload")
				auto_reload()
			else:
				emit_signal("no_ammo", self)
			return false

		_fire()

	return true

func _dual_wield_fire():
	"""Fire alternating between left and right guns"""
	# Only the right-hand gun (main gun) should handle firing logic
	if is_left_hand:
		return

	# Check which gun should fire and if it has ammo
	var firing_gun = self
	var firing_left = alternate_fire

	if firing_left:
		if left_clip_ammo <= 0:
			# Left gun empty, check if we can auto-reload or switch
			if dual_wield_ammo > 0:
				# Trigger automatic reload for left gun
				print("PP8: Left clip empty - starting automatic reload")
				play_sound(empty_sound)
				_auto_reload_dual_wield_gun(true)  # true = left gun
				return
			elif right_clip_ammo <= 0:
				# Both guns empty and no reserve
				play_sound(empty_sound)
				emit_signal("no_ammo", self)
				return
			else:
				# Switch to right gun
				firing_left = false
				firing_gun = self

		if firing_left:
			firing_gun = dual_wield_partner if dual_wield_partner else self
	else:
		if right_clip_ammo <= 0:
			# Right gun empty, check if we can auto-reload or switch
			if dual_wield_ammo > 0:
				# Trigger automatic reload for right gun
				print("PP8: Right clip empty - starting automatic reload")
				play_sound(empty_sound)
				_auto_reload_dual_wield_gun(false)  # false = right gun
				return
			elif left_clip_ammo <= 0:
				# Both guns empty and no reserve
				play_sound(empty_sound)
				emit_signal("no_ammo", self)
				return
			else:
				# Switch to left gun
				firing_left = true
				firing_gun = dual_wield_partner if dual_wield_partner else self

		if not firing_left:
			firing_gun = self

	# Reduce ammo from the appropriate clip
	if firing_left:
		left_clip_ammo -= 1
	else:
		right_clip_ammo -= 1

	# Update display values (show right gun's ammo on HUD)
	current_ammo = right_clip_ammo
	reserve_ammo = dual_wield_ammo

	# Update partner's values
	if dual_wield_partner:
		dual_wield_partner.left_clip_ammo = left_clip_ammo
		dual_wield_partner.right_clip_ammo = right_clip_ammo
		dual_wield_partner.current_ammo = left_clip_ammo
		dual_wield_partner.reserve_ammo = dual_wield_ammo

	emit_signal("ammo_changed", current_ammo, reserve_ammo)

	# Set cooldown for both guns
	can_fire = false
	fire_cooldown = 1.0 / fire_rate
	if dual_wield_partner:
		dual_wield_partner.can_fire = false
		dual_wield_partner.fire_cooldown = 1.0 / fire_rate

	# Alternate for next shot
	alternate_fire = !alternate_fire

	# Play effects on the firing gun
	_play_fire_effects(firing_gun)

	# Handle hit detection from the firing gun's position
	_handle_dual_wield_hit_detection(firing_gun.muzzle_position if firing_gun.muzzle_position else firing_gun)

	emit_signal("weapon_fired", self)

func _auto_reload_dual_wield_gun(is_left_gun: bool):
	"""Auto-reload a specific gun in dual-wield mode with animation"""
	if is_reloading:
		return

	# Only the right-hand gun should handle reload logic
	if is_left_hand:
		return

	is_reloading = true
	if dual_wield_partner:
		dual_wield_partner.is_reloading = true

	var gun_name = "left" if is_left_gun else "right"
	print("PP8: Starting automatic reload for " + gun_name + " gun")

	# Play reload animation on the appropriate gun
	var reloading_gun = dual_wield_partner if is_left_gun else self
	if reloading_gun:
		await reloading_gun._play_reload_animation()

	# Perform the reload
	if is_left_gun:
		var reload_amount = min(ammo_per_clip, dual_wield_ammo)
		left_clip_ammo = reload_amount
		dual_wield_ammo -= reload_amount
		print("PP8: Auto-reloaded left gun with " + str(reload_amount) + " rounds")
	else:
		var reload_amount = min(ammo_per_clip, dual_wield_ammo)
		right_clip_ammo = reload_amount
		dual_wield_ammo -= reload_amount
		print("PP8: Auto-reloaded right gun with " + str(reload_amount) + " rounds")

	# Update display values
	current_ammo = right_clip_ammo
	reserve_ammo = dual_wield_ammo

	# Update partner
	if dual_wield_partner:
		dual_wield_partner.left_clip_ammo = left_clip_ammo
		dual_wield_partner.right_clip_ammo = right_clip_ammo
		dual_wield_partner.dual_wield_ammo = dual_wield_ammo
		dual_wield_partner.current_ammo = left_clip_ammo
		dual_wield_partner.reserve_ammo = dual_wield_ammo
		dual_wield_partner.is_reloading = false

	is_reloading = false
	emit_signal("ammo_changed", current_ammo, reserve_ammo)
	emit_signal("weapon_reloaded", self)

func _play_fire_effects(gun: Node3D):
	"""Play fire effects on the specified gun"""
	# Play animation on the firing gun
	var anim_player = gun.get_node("AnimationPlayer") if gun.has_node("AnimationPlayer") else null
	if anim_player and anim_player.has_animation(fire_animation):
		anim_player.play(fire_animation)

	# Play sound (use main gun's audio)
	play_sound(fire_sound)

	# Spawn muzzle flash on the firing gun
	if muzzle_flash:
		var muzzle_pos = gun.get_node("MuzzlePosition") if gun.has_node("MuzzlePosition") else gun
		if muzzle_pos:
			var flash_instance = muzzle_flash.instantiate()
			muzzle_pos.add_child(flash_instance)
			# Auto-remove after a short time
			var timer = Timer.new()
			timer.process_mode = Node.PROCESS_MODE_ALWAYS
			timer.wait_time = 0.1
			timer.one_shot = true
			add_child(timer)
			timer.start()
			await timer.timeout
			timer.queue_free()
			if flash_instance and is_instance_valid(flash_instance):
				flash_instance.queue_free()

func _handle_dual_wield_hit_detection(firing_position: Node3D):
	"""Handle hit detection from the specified firing position"""
	var firing_raycast = firing_position.get_node("../RayCast3D") if firing_position.has_node("../RayCast3D") else raycast

	if firing_raycast and firing_raycast.is_colliding():
		var target = firing_raycast.get_collider()
		var hit_position = firing_raycast.get_collision_point()
		var hit_normal = firing_raycast.get_collision_normal()

		print(weapon_name + ": Hit detected at " + str(hit_position))

		# Spawn impact effect
		if impact_effect:
			var impact = impact_effect.instantiate()
			get_tree().get_root().add_child(impact)
			impact.global_transform.origin = hit_position

			# Orient the impact effect to face the normal
			var y = hit_normal
			var x = y.cross(Vector3.UP)
			if x.length_squared() < 0.001:
				x = y.cross(Vector3.RIGHT)
			var z = x.cross(y)
			impact.global_transform.basis = Basis(x.normalized(), y.normalized(), z.normalized())

			# Auto-remove after a short time
			var timer = Timer.new()
			timer.process_mode = Node.PROCESS_MODE_ALWAYS
			timer.wait_time = 2.0
			timer.one_shot = true
			add_child(timer)
			timer.start()
			await timer.timeout
			timer.queue_free()
			if impact and is_instance_valid(impact):
				impact.queue_free()

		# Apply damage if target has health
		if target.has_method("take_damage"):
			target.take_damage(damage, hit_position, hit_normal)
			print(weapon_name + ": Applied " + str(damage) + " damage to " + str(target))

		emit_signal("hit_target", target, damage, hit_position, hit_normal)

func reload():
	if is_reloading:
		print("INFO: " + weapon_name + " - Already reloading")
		return

	if is_dual_wielding:
		# Only the right-hand gun should handle reload logic
		if is_left_hand:
			return

		# Check if both clips are full
		if left_clip_ammo >= ammo_per_clip and right_clip_ammo >= ammo_per_clip:
			print("INFO: " + weapon_name + " - Both magazines already full")
			return

		# Check if we have reserve ammo
		if dual_wield_ammo <= 0:
			print("INFO: " + weapon_name + " - No reserve ammo available")
			return
	else:
		if current_ammo == ammo_per_clip:
			print("INFO: " + weapon_name + " - Magazine already full")
			return

		if reserve_ammo <= 0:
			print("INFO: " + weapon_name + " - No reserve ammo available")
			return

	is_reloading = true
	print("INFO: " + weapon_name + " - Reloading started")
	play_sound(reload_sound)

	# Set both guns to reloading state
	if is_dual_wielding and dual_wield_partner:
		dual_wield_partner.is_reloading = true

	# Play reload animation
	if animation_player and animation_player.has_animation(reload_animation):
		animation_player.play(reload_animation)
		await animation_player.animation_finished
	else:
		var timer = Timer.new()
		timer.process_mode = Node.PROCESS_MODE_ALWAYS
		timer.wait_time = reload_time
		timer.one_shot = true
		add_child(timer)
		timer.start()
		await timer.timeout
		timer.queue_free()

	if is_dual_wielding:
		# Reload both clips from shared reserve
		var left_needed = max(0, ammo_per_clip - left_clip_ammo)
		var right_needed = max(0, ammo_per_clip - right_clip_ammo)
		var total_needed = left_needed + right_needed
		var ammo_to_use = min(dual_wield_ammo, total_needed)

		# Fill left clip first, then right clip
		if left_needed > 0:
			var left_reload = min(left_needed, ammo_to_use)
			left_clip_ammo += left_reload
			dual_wield_ammo -= left_reload
			ammo_to_use -= left_reload

		if right_needed > 0 and ammo_to_use > 0:
			var right_reload = min(right_needed, ammo_to_use)
			right_clip_ammo += right_reload
			dual_wield_ammo -= right_reload

		# Update display values
		current_ammo = right_clip_ammo
		reserve_ammo = dual_wield_ammo

		# Update partner
		if dual_wield_partner:
			dual_wield_partner.left_clip_ammo = left_clip_ammo
			dual_wield_partner.right_clip_ammo = right_clip_ammo
			dual_wield_partner.dual_wield_ammo = dual_wield_ammo
			dual_wield_partner.current_ammo = left_clip_ammo
			dual_wield_partner.reserve_ammo = dual_wield_ammo
			dual_wield_partner.is_reloading = false
	else:
		var ammo_needed = ammo_per_clip - current_ammo
		var ammo_to_reload = min(ammo_needed, reserve_ammo)
		current_ammo += ammo_to_reload
		reserve_ammo -= ammo_to_reload

	print("INFO: " + weapon_name + " - Reload complete. Left: " + str(left_clip_ammo) + " Right: " + str(right_clip_ammo) + " Reserve: " + str(dual_wield_ammo))

	is_reloading = false
	emit_signal("weapon_reloaded", self)
	emit_signal("ammo_changed", current_ammo, reserve_ammo)

func equip():
	super.equip()

	# Also equip the dual-wield partner
	if is_dual_wielding and dual_wield_partner and not is_left_hand:
		dual_wield_partner.is_equipped = true
		dual_wield_partner.visible = true

func unequip():
	super.unequip()

	# Also unequip the dual-wield partner
	if is_dual_wielding and dual_wield_partner and not is_left_hand:
		dual_wield_partner.is_equipped = false
		dual_wield_partner.visible = false

func get_ammo_info() -> Dictionary:
	if is_dual_wielding:
		return {
			"current": current_ammo,
			"reserve": reserve_ammo,
			"max": max_ammo * 2,  # Double max for dual wield
			"per_clip": ammo_per_clip,
			"dual_wield": true,
			"left_clip": left_clip_ammo,
			"right_clip": right_clip_ammo
		}
	else:
		return super.get_ammo_info()
