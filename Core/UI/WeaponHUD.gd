extends Control
class_name WeaponHUD

# References to UI elements
@onready var weapon_name_label: Label = $WeaponNameLabel
@onready var ammo_label: Label = $AmmoLabel
@onready var weapon_icon: TextureRect = $WeaponIcon
@onready var speed_label: Label = $SpeedLabel

# Reference to weapon manager
@export var weapon_manager: Node # Changed from WeaponManager to Node

# Player reference for speed tracking
var player: PlayerController

func _ready():
	# Connect signals from weapon manager
	if weapon_manager:
		weapon_manager.weapon_switched.connect(_on_weapon_switched)
		weapon_manager.ammo_changed.connect(_on_ammo_changed)
		weapon_manager.weapon_added.connect(_on_weapon_added)
	else:
		# Try to find weapon manager in the scene
		var player_node = get_tree().get_first_node_in_group("player")
		if player_node:
			var manager = _find_weapon_manager(player_node)
			if manager:
				weapon_manager = manager
				weapon_manager.weapon_switched.connect(_on_weapon_switched)
				weapon_manager.ammo_changed.connect(_on_ammo_changed)
				weapon_manager.weapon_added.connect(_on_weapon_added)

	# Initialize UI
	update_ui(null)

	# Find player for speed tracking
	_find_player()

	print("WeaponHUD initialized")

func _find_weapon_manager(node):
	# Try direct reference
	if node.has_node("WeaponManager"):
		return node.get_node("WeaponManager")

	# Try to find in camera mount
	if node.has_node("CameraMount/Camera/WeaponManager"):
		return node.get_node("CameraMount/Camera/WeaponManager")

	# Try to find in children
	for child in node.get_children():
		if child is WeaponManager:
			return child

	return null

func _on_weapon_switched(weapon):
	update_ui(weapon)

func _on_ammo_changed(_weapon, current, reserve):
	update_ammo(current, reserve)

func _on_weapon_added(weapon, _index):
	# If this is our first weapon, update the UI
	if weapon_manager.get_current_weapon() == weapon:
		update_ui(weapon)

func update_ui(weapon):
	if weapon == null:
		# Hide weapon UI when no weapon is equipped
		weapon_name_label.visible = false
		ammo_label.visible = false
		weapon_icon.visible = false
		return

	# Show weapon UI when weapon is equipped
	weapon_name_label.visible = true
	ammo_label.visible = true
	weapon_icon.visible = true

	# Update weapon name
	weapon_name_label.text = weapon.weapon_name

	# Update ammo
	var ammo_info = weapon.get_ammo_info()
	update_ammo(ammo_info.current, ammo_info.reserve)

	# Update weapon icon
	weapon_icon.texture = weapon.weapon_icon

func update_ammo(current, reserve):
	# Check if current weapon is dual-wield PP8
	if weapon_manager and weapon_manager.get_current_weapon():
		var weapon = weapon_manager.get_current_weapon()
		if weapon.has_method("get_ammo_info"):
			var ammo_info = weapon.get_ammo_info()
			if ammo_info.has("dual_wield") and ammo_info.dual_wield:
				# Show both clips for dual-wield
				var left_clip = ammo_info.get("left_clip", 0)
				var right_clip = ammo_info.get("right_clip", 0)
				ammo_label.text = str(int(left_clip)) + " | " + str(int(right_clip)) + " / " + str(int(reserve))
				return

	# Default single weapon display
	ammo_label.text = str(int(current)) + " / " + str(int(reserve))

func _find_player():
	# Try to find the player in the scene
	var scene_root = get_tree().current_scene
	if scene_root:
		player = _find_player_recursive(scene_root)

func _find_player_recursive(node: Node) -> PlayerController:
	if node is PlayerController:
		return node

	for child in node.get_children():
		var result = _find_player_recursive(child)
		if result:
			return result

	return null

func _process(_delta):
	# Update speed display
	if player and speed_label:
		var horizontal_velocity = Vector3(player.velocity.x, 0, player.velocity.z)
		var speed = horizontal_velocity.length()
		speed_label.text = "Speed: " + str(int(speed * 10) / 10.0) + " m/s"

		# Change color based on speed for visual feedback
		if speed > player.run_speed * 1.2:  # Bunny hop speed
			speed_label.modulate = Color.GREEN
		elif speed > player.run_speed:
			speed_label.modulate = Color.YELLOW
		else:
			speed_label.modulate = Color.WHITE
