[gd_scene load_steps=3 format=3 uid="uid://bwx8y9z0a1b2c"]

[ext_resource type="Script" path="res://Core/UI/WeaponCustomization.gd" id="1_customization"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_panel"]
bg_color = Color(0.1, 0.1, 0.1, 0.9)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="WeaponCustomization" type="CanvasLayer"]
process_mode = 3
layer = 102
script = ExtResource("1_customization")

[node name="CustomizationContainer" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Background" type="Panel" parent="CustomizationContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_panel")

[node name="Title" type="Label" parent="CustomizationContainer"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_top = 20.0
offset_bottom = 60.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 28
text = "Weapon Customization"
horizontal_alignment = 1

[node name="VBoxContainer" type="VBoxContainer" parent="CustomizationContainer"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 50.0
offset_top = -200.0
offset_right = 400.0
offset_bottom = 200.0
grow_vertical = 2
theme_override_constants/separation = 20

[node name="CrosshairSizeContainer" type="HBoxContainer" parent="CustomizationContainer/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="CustomizationContainer/VBoxContainer/CrosshairSizeContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Crosshair Size:"

[node name="SizeSlider" type="HSlider" parent="CustomizationContainer/VBoxContainer/CrosshairSizeContainer"]
layout_mode = 2
size_flags_horizontal = 3
min_value = 5.0
max_value = 50.0
step = 1.0
value = 20.0

[node name="CrosshairThicknessContainer" type="HBoxContainer" parent="CustomizationContainer/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="CustomizationContainer/VBoxContainer/CrosshairThicknessContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Crosshair Thickness:"

[node name="ThicknessSlider" type="HSlider" parent="CustomizationContainer/VBoxContainer/CrosshairThicknessContainer"]
layout_mode = 2
size_flags_horizontal = 3
min_value = 1.0
max_value = 10.0
step = 0.5
value = 2.0

[node name="CrosshairGapContainer" type="HBoxContainer" parent="CustomizationContainer/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="CustomizationContainer/VBoxContainer/CrosshairGapContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Crosshair Gap:"

[node name="GapSlider" type="HSlider" parent="CustomizationContainer/VBoxContainer/CrosshairGapContainer"]
layout_mode = 2
size_flags_horizontal = 3
min_value = 0.0
max_value = 20.0
step = 1.0
value = 5.0

[node name="CrosshairColorContainer" type="VBoxContainer" parent="CustomizationContainer/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="CustomizationContainer/VBoxContainer/CrosshairColorContainer"]
layout_mode = 2
text = "Crosshair Color:"

[node name="ColorPicker" type="ColorPicker" parent="CustomizationContainer/VBoxContainer/CrosshairColorContainer"]
layout_mode = 2
color = Color(1, 1, 1, 0.8)

[node name="CrosshairPreview" type="Control" parent="CustomizationContainer"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -150.0
offset_right = -50.0
offset_bottom = 150.0
grow_horizontal = 0
grow_vertical = 2

[node name="PreviewBackground" type="Panel" parent="CustomizationContainer/CrosshairPreview"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="PreviewLabel" type="Label" parent="CustomizationContainer/CrosshairPreview"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 30.0
grow_horizontal = 2
text = "Crosshair Preview"
horizontal_alignment = 1

[node name="CloseButton" type="Button" parent="CustomizationContainer"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -50.0
offset_top = -60.0
offset_right = 50.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 0
text = "Close"
