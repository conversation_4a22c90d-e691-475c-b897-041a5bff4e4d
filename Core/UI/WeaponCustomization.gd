extends CanvasLayer
class_name WeaponCustomization

# References
@onready var customization_container: Control = $CustomizationContainer
@onready var crosshair_preview: Control = $CustomizationContainer/CrosshairPreview
@onready var crosshair_size_slider: HSlider = $CustomizationContainer/VBoxContainer/CrosshairSizeContainer/SizeSlider
@onready var crosshair_thickness_slider: HSlider = $CustomizationContainer/VBoxContainer/CrosshairThicknessContainer/ThicknessSlider
@onready var crosshair_gap_slider: HSlider = $CustomizationContainer/VBoxContainer/CrosshairGapContainer/GapSlider
@onready var crosshair_color_picker: ColorPicker = $CustomizationContainer/VBoxContainer/CrosshairColorContainer/ColorPicker
@onready var close_button: Button = $CustomizationContainer/CloseButton

# Crosshair settings
var crosshair_size: float = 20.0
var crosshair_thickness: float = 2.0
var crosshair_gap: float = 5.0
var crosshair_color: Color = Color(1, 1, 1, 0.8)

# Player reference for updating crosshair
var player: PlayerController

# Signals
signal customization_closed

func _ready():
	# Hide initially
	customization_container.visible = false

	# Connect signals
	close_button.pressed.connect(_on_close_button_pressed)
	crosshair_size_slider.value_changed.connect(_on_size_changed)
	crosshair_thickness_slider.value_changed.connect(_on_thickness_changed)
	crosshair_gap_slider.value_changed.connect(_on_gap_changed)
	crosshair_color_picker.color_changed.connect(_on_color_changed)

	# Set initial values
	crosshair_size_slider.value = crosshair_size
	crosshair_thickness_slider.value = crosshair_thickness
	crosshair_gap_slider.value = crosshair_gap
	crosshair_color_picker.color = crosshair_color

	# Setup crosshair preview drawing
	if crosshair_preview:
		# Create a custom control for drawing
		var preview_control = Control.new()
		preview_control.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		crosshair_preview.add_child(preview_control)
		preview_control.draw.connect(_draw_crosshair_preview)

	# Find player
	_find_player()

func _find_player():
	# Try to find the player in the scene
	var scene_root = get_tree().current_scene
	if scene_root:
		player = _find_player_recursive(scene_root)

func _find_player_recursive(node: Node) -> PlayerController:
	if node is PlayerController:
		return node

	for child in node.get_children():
		var result = _find_player_recursive(child)
		if result:
			return result

	return null

func show_customization():
	customization_container.visible = true
	Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
	get_tree().paused = true

func hide_customization():
	customization_container.visible = false
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
	get_tree().paused = false
	emit_signal("customization_closed")

func _on_close_button_pressed():
	hide_customization()

func _on_size_changed(value: float):
	crosshair_size = value
	_update_crosshair_preview()
	_update_player_crosshair()

func _on_thickness_changed(value: float):
	crosshair_thickness = value
	_update_crosshair_preview()
	_update_player_crosshair()

func _on_gap_changed(value: float):
	crosshair_gap = value
	_update_crosshair_preview()
	_update_player_crosshair()

func _on_color_changed(color: Color):
	crosshair_color = color
	_update_crosshair_preview()
	_update_player_crosshair()

func _update_crosshair_preview():
	if crosshair_preview and crosshair_preview.get_child_count() > 0:
		var preview_control = crosshair_preview.get_child(0)
		if preview_control:
			preview_control.queue_redraw()

func _update_player_crosshair():
	if player:
		var crosshair_ui = player.get_node_or_null("CrosshairUI")
		if crosshair_ui:
			crosshair_ui.crosshair_size = crosshair_size
			crosshair_ui.crosshair_thickness = crosshair_thickness
			crosshair_ui.crosshair_gap = crosshair_gap
			crosshair_ui.crosshair_color = crosshair_color
			crosshair_ui.queue_redraw()

func _draw_crosshair_preview():
	if not crosshair_preview or crosshair_preview.get_child_count() == 0:
		return

	var preview_control = crosshair_preview.get_child(0)
	if not preview_control:
		return

	var center = Vector2(preview_control.size.x / 2, preview_control.size.y / 2)

	# Draw crosshair lines
	# Top line
	preview_control.draw_line(
		Vector2(center.x, center.y - crosshair_gap - crosshair_size),
		Vector2(center.x, center.y - crosshair_gap),
		crosshair_color,
		crosshair_thickness
	)

	# Bottom line
	preview_control.draw_line(
		Vector2(center.x, center.y + crosshair_gap),
		Vector2(center.x, center.y + crosshair_gap + crosshair_size),
		crosshair_color,
		crosshair_thickness
	)

	# Left line
	preview_control.draw_line(
		Vector2(center.x - crosshair_gap - crosshair_size, center.y),
		Vector2(center.x - crosshair_gap, center.y),
		crosshair_color,
		crosshair_thickness
	)

	# Right line
	preview_control.draw_line(
		Vector2(center.x + crosshair_gap, center.y),
		Vector2(center.x + crosshair_gap + crosshair_size, center.y),
		crosshair_color,
		crosshair_thickness
	)

# Custom control for crosshair preview
class CrosshairPreviewControl extends Control:
	var parent_customization: WeaponCustomization

	func _draw():
		if parent_customization:
			parent_customization._draw_crosshair_preview()
