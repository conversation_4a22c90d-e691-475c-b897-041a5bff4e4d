extends Control
class_name CrosshairUI

# Crosshair properties
@export var crosshair_size: float = 20.0
@export var crosshair_thickness: float = 2.0
@export var crosshair_gap: float = 5.0
@export var crosshair_color: Color = Color(1, 1, 1, 0.8)
@export var aim_color: Color = Color(1, 0, 0, 0.8)

# State
var is_aiming: bool = false

func _ready():
	# Hide crosshair initially
	visible = false

	# Connect to player signals if possible
	var player = get_parent()
	while player and not player is PlayerController:
		player = player.get_parent()

	if player and player is PlayerController:
		player.player_aiming.connect(_on_player_aiming)

func _draw():
	if not visible:
		return

	var center = Vector2(size.x / 2, size.y / 2)
	var color = aim_color if is_aiming else crosshair_color

	# Draw crosshair lines
	# Top line
	draw_line(
		Vector2(center.x, center.y - crosshair_gap - crosshair_size),
		Vector2(center.x, center.y - crosshair_gap),
		color,
		crosshair_thickness
	)

	# Bottom line
	draw_line(
		Vector2(center.x, center.y + crosshair_gap),
		Vector2(center.x, center.y + crosshair_gap + crosshair_size),
		color,
		crosshair_thickness
	)

	# Left line
	draw_line(
		Vector2(center.x - crosshair_gap - crosshair_size, center.y),
		Vector2(center.x - crosshair_gap, center.y),
		color,
		crosshair_thickness
	)

	# Right line
	draw_line(
		Vector2(center.x + crosshair_gap, center.y),
		Vector2(center.x + crosshair_gap + crosshair_size, center.y),
		color,
		crosshair_thickness
	)

func _on_player_aiming(aiming: bool):
	is_aiming = aiming

	# Only show crosshair if aiming AND we have a weapon
	var should_show = aiming and _player_has_weapon()
	visible = should_show
	queue_redraw()

func _player_has_weapon() -> bool:
	# Find player and check if they have a weapon
	var player = get_parent()
	while player and not player is PlayerController:
		player = player.get_parent()

	if player and player is PlayerController:
		var weapon_manager = player.get_weapon_manager()
		if weapon_manager and weapon_manager.has_method("get_current_weapon"):
			return weapon_manager.get_current_weapon() != null

	return false
