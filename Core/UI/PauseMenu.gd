extends <PERSON>vas<PERSON><PERSON>er
class_name <PERSON><PERSON><PERSON><PERSON><PERSON>

@export var options_menu: <PERSON><PERSON><PERSON><PERSON><PERSON>
@export var input_manager: Node # Changed from InputManager to Node

var pause_menu_enabled: bool = false

# References
@onready var resume_button: Button = $PauseMenuContainer/VBoxContainer/ResumeButton
@onready var options_button: Button = $PauseMenuContainer/VBoxContainer/OptionsButton
@onready var quit_button: Button = $PauseMenuContainer/VBoxContainer/QuitButton
@onready var pause_menu_container: Control = $PauseMenuContainer

func _ready():
    # Connect button signals
    resume_button.pressed.connect(_on_resume_button_pressed)
    options_button.pressed.connect(_on_options_button_pressed)
    quit_button.pressed.connect(_on_quit_button_pressed)

    # Connect to input manager
    if input_manager:
        print("PauseMenu: Using provided input_manager path: ", input_manager)
        input_manager.pause_toggled.connect(_on_pause_toggled)
    else:
        # Try to find the input manager in the autoload singletons
        var input_manager_node = get_node_or_null("/root/InputManager")
        if input_manager_node:
            print("PauseMenu: Found InputManager in autoload")
            input_manager = input_manager_node
            input_manager.pause_toggled.connect(_on_pause_toggled)
        else:
            print("PauseMenu: WARNING - No InputManager found!")

    # Initialize
    set_pause_menu(false)
    # Make sure the pause menu is initially hidden
    pause_menu_container.visible = false
    print("PauseMenu initialized")

func _process(_delta):
    # Debug check for Esc key
    if Input.is_action_just_pressed("pauseMenu"):
        print("PauseMenu: Detected pauseMenu action (Esc key)")

func _input(event):
    # Handle pause menu toggle
    if event.is_action_pressed("pauseMenu"):
        print("PauseMenu: pauseMenu action pressed in _input")
        if options_menu and options_menu.is_visible():
            # Don't toggle pause if options menu is open
            print("PauseMenu: Options menu is visible, ignoring pause toggle")
            return

        if input_manager:
            print("PauseMenu: Calling input_manager.toggle_pause()")
            input_manager.toggle_pause()
        else:
            print("PauseMenu: No input manager, calling toggle_pause_menu() directly")
            toggle_pause_menu()

func set_pause_menu(value: bool):
    pause_menu_enabled = value
    pause_menu_container.visible = value

    print("PauseMenu: Setting pause state to: ", value, ", Menu visibility: ", pause_menu_container.visible)

    # Update game state
    if pause_menu_enabled:
        # Use get_tree().paused instead of Engine.time_scale
        get_tree().paused = true
        if input_manager:
            input_manager.release_mouse()
        else:
            Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
    else:
        # Use get_tree().paused instead of Engine.time_scale
        get_tree().paused = false
        if input_manager:
            input_manager.capture_mouse()
        else:
            Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func toggle_pause_menu():
    print("PauseMenu: Toggling pause menu from ", pause_menu_enabled, " to ", !pause_menu_enabled)
    set_pause_menu(!pause_menu_enabled)

func _on_resume_button_pressed():
    print("PauseMenu: Resume button pressed")
    if input_manager:
        input_manager.toggle_pause()
    else:
        set_pause_menu(false)

func _on_options_button_pressed():
    if options_menu:
        # Hide pause menu but keep game paused
        pause_menu_container.visible = false
        options_menu.show_options_menu()

        # Connect to options closed signal if not already connected
        if not options_menu.options_closed.is_connected(_on_options_closed):
            options_menu.options_closed.connect(_on_options_closed)

func _on_options_closed():
    # Show pause menu again when options are closed
    pause_menu_container.visible = true

func _on_quit_button_pressed():
    print("PauseMenu: Quit button pressed")
    get_tree().quit()

func _on_pause_toggled(is_paused: bool):
    # Force visibility update
    print("PauseMenu: Pause toggled signal received with value: ", is_paused)
    pause_menu_container.visible = is_paused
    set_pause_menu(is_paused)

    # Debug visibility state
    print("PauseMenu: After toggle - Pause state: ", is_paused, ", Menu visibility: ", pause_menu_container.visible)

func is_menu_paused() -> bool:
    return pause_menu_enabled
