extends CanvasLayer
class_name OptionsMenu

# Simple options menu implementation
@onready var options_container: Control = $OptionsContainer if has_node("OptionsContainer") else null
@onready var back_button: Button = null

# Signal when options are closed
signal options_closed

func _ready():
	print("OptionsMenu: _ready() called")

	# Try to find back button in various locations
	if options_container:
		back_button = options_container.get_node_or_null("BackButton")
		if not back_button:
			back_button = options_container.get_node_or_null("VBoxContainer/BackButton")
		if not back_button:
			back_button = options_container.get_node_or_null("TabContainer/BackButton")

	# Connect back button if found
	if back_button:
		back_button.pressed.connect(_on_back_button_pressed)
		print("OptionsMenu: Back button connected")
	else:
		print("OptionsMenu: Back button not found, creating fallback")
		_create_fallback_ui()

	# Hide menu initially
	if options_container:
		options_container.visible = false
	else:
		visible = false

func _create_fallback_ui():
	# Create a simple fallback UI if the scene structure is missing
	var vbox = VBoxContainer.new()
	add_child(vbox)

	var title = Label.new()
	title.text = "OPTIONS"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(title)

	var spacer = Control.new()
	spacer.custom_minimum_size = Vector2(0, 50)
	vbox.add_child(spacer)

	# Create back button
	back_button = Button.new()
	back_button.text = "BACK"
	back_button.pressed.connect(_on_back_button_pressed)
	vbox.add_child(back_button)

	# Center the VBox
	vbox.set_anchors_and_offsets_preset(Control.PRESET_CENTER)

func show_options_menu():
	print("OptionsMenu: Showing options")
	if options_container:
		options_container.visible = true
	else:
		visible = true

func hide_options_menu():
	print("OptionsMenu: Hiding options")
	if options_container:
		options_container.visible = false
	else:
		visible = false
	emit_signal("options_closed")

func _on_back_button_pressed():
	print("OptionsMenu: Back button pressed")
	hide_options_menu()
