[gd_scene load_steps=15 format=3 uid="uid://dr7uppovq5rbl"]

[ext_resource type="PackedScene" uid="uid://b3yxcv7jwaq8r" path="res://Core/Player/Player.tscn" id="1_jlim3"]
[ext_resource type="PackedScene" uid="uid://c6k1du6osiry0" path="res://Core/UI/PauseMenu.tscn" id="2_umody"]
[ext_resource type="Script" uid="uid://dkuok504vr60e" path="res://Core/Utils/JumpPad.gd" id="3_s510l"]
[ext_resource type="PackedScene" uid="uid://6k1du6osiry0" path="res://Core/Pickups/Resources/PistolPickup.tscn" id="4_dtt05"]
[ext_resource type="PackedScene" uid="uid://b6k1du6osiry0" path="res://Core/Pickups/Resources/ShotgunPickup.tscn" id="5_2420q"]
[ext_resource type="PackedScene" uid="uid://bb55rtaf8bv78" path="res://Core/Pickups/Resources/PP8Pickup.tscn" id="6_pp8"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_sky"]
sky_horizon_color = Color(0.64625, 0.65575, 0.67075, 1)
ground_horizon_color = Color(0.64625, 0.65575, 0.67075, 1)

[sub_resource type="Sky" id="Sky_sky"]
sky_material = SubResource("ProceduralSkyMaterial_sky")

[sub_resource type="Environment" id="Environment_env"]
background_mode = 2
sky = SubResource("Sky_sky")
tonemap_mode = 2
glow_enabled = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_floor"]
albedo_color = Color(0.2, 0.2, 0.2, 1)
metallic = 0.1
roughness = 0.8

[sub_resource type="BoxMesh" id="BoxMesh_platform"]
material = SubResource("StandardMaterial3D_floor")
size = Vector3(50, 1, 50)

[sub_resource type="BoxShape3D" id="BoxShape3D_platform"]
size = Vector3(50, 1, 50)

[sub_resource type="CylinderMesh" id="CylinderMesh_jumppad"]
top_radius = 2.0
bottom_radius = 2.0
height = 0.2

[sub_resource type="CylinderShape3D" id="CylinderShape3D_jumppad"]
height = 0.2
radius = 2.0

[node name="Level1" type="Node3D"]

[node name="Environment" type="Node3D" parent="."]

[node name="WorldEnvironment" type="WorldEnvironment" parent="Environment"]
environment = SubResource("Environment_env")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="Environment"]
transform = Transform3D(-0.866023, -0.433016, 0.250001, 0, 0.499998, 0.866027, -0.500003, 0.749999, -0.43301, 0, 0, 0)
shadow_enabled = true

[node name="Player" parent="." instance=ExtResource("1_jlim3")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6.91344, 20.8723, 8.366)

[node name="PauseMenu" parent="." instance=ExtResource("2_umody")]
input_manager = NodePath("/root/InputManager")

[node name="Map" type="Node3D" parent="."]

[node name="Floor" type="StaticBody3D" parent="Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 2.01105, 0, -0.5, 0)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/Floor"]
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/Floor"]
shape = SubResource("BoxShape3D_platform")

[node name="Platform1" type="StaticBody3D" parent="Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15, 2, -15)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/Platform1"]
transform = Transform3D(0.852656, 0, 0, 0, 0.197034, -0.0857926, 0, 0.034317, 0.492585, 0, 0, 0)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/Platform1"]
transform = Transform3D(0.852656, 0, 0, 0, 0.2, 0, 0, 0, 0.5, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="Platform2" type="StaticBody3D" parent="Map"]
transform = Transform3D(0.740455, -0.357907, 0.000549879, 0.283814, 0.933757, 0.000448654, 0.00144943, 0.000602371, -0.368762, 12.1631, 1.03337, -12.2902)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/Platform2"]
transform = Transform3D(0.5, 0, 0, 0, 0.2, 0, 0, 0, 0.5, 0, 0, 0)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/Platform2"]
transform = Transform3D(0.5, 0, 0, 0, 0.2, 0, 0, 0, 0.5, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="Platform3" type="StaticBody3D" parent="Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11.7592, 6, -20)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/Platform3"]
transform = Transform3D(0.308953, 0, 0, 0, 0.0571155, 0, 0, 0, 0.382359, 18.2344, -1.07525, 2.83191)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/Platform3"]
transform = Transform3D(0.5, 0, 0, 0, 0.2, 0, 0, 0, 0.5, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="JumpPad" type="Area3D" parent="Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.1, 15)
collision_mask = 2
script = ExtResource("3_s510l")

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/JumpPad"]
mesh = SubResource("CylinderMesh_jumppad")
skeleton = NodePath("../..")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/JumpPad"]
shape = SubResource("CylinderShape3D_jumppad")

[node name="AudioStreamPlayer3D" type="AudioStreamPlayer3D" parent="Map/JumpPad"]

[node name="TestWalls" type="Node3D" parent="Map"]

[node name="WallRunWall1" type="StaticBody3D" parent="Map/TestWalls"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 22.6872, 0)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/TestWalls/WallRunWall1"]
transform = Transform3D(0.2, 0, 0, 0, 5, 0, 0, 0, 10, 0, 0, 0)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/TestWalls/WallRunWall1"]
transform = Transform3D(0.2, 0, 0, 0, 5, 0, 0, 0, 10, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="WallRunWall2" type="StaticBody3D" parent="Map/TestWalls"]
transform = Transform3D(1, 0, 0, 0, 8.03952, 0, 0, 0, 1, 20, 28.1107, 0)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/TestWalls/WallRunWall2"]
transform = Transform3D(0.2, 0, 0, 0, 5, 0, 0, 0, 10, 0, 0, 0)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/TestWalls/WallRunWall2"]
transform = Transform3D(0.2, 0, 0, 0, 5, 0, 0, 0, 10, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="TestRamps" type="Node3D" parent="Map"]

[node name="Ramp1" type="StaticBody3D" parent="Map/TestRamps"]
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, -10, 2, 5)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/TestRamps/Ramp1"]
transform = Transform3D(2, 0, 0, 0, 0.2, 0, 0, 0, 5, 0, 0, 0)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/TestRamps/Ramp1"]
transform = Transform3D(2, 0, 0, 0, 0.2, 0, 0, 0, 5, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="Ramp2" type="StaticBody3D" parent="Map/TestRamps"]
transform = Transform3D(1, 0, 0, 0, 0.866025, -0.5, 0, 0.5, 0.866025, 10, 2, 5)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/TestRamps/Ramp2"]
transform = Transform3D(2, 0, 0, 0, 0.2, 0, 0, 0, 5, 0, 0, 0)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/TestRamps/Ramp2"]
transform = Transform3D(2, 0, 0, 0, 0.2, 0, 0, 0, 5, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="HighPlatforms" type="Node3D" parent="Map"]

[node name="HighPlatform1" type="StaticBody3D" parent="Map/HighPlatforms"]
transform = Transform3D(1.01477, 0, 0, 0, 1.01477, 0, 0, 0, 1.01477, -15, 8, 15)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/HighPlatforms/HighPlatform1"]
transform = Transform3D(3, 0, 0, 0, 0.2, 0, 0, 0, 3, 0, 0, 0)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/HighPlatforms/HighPlatform1"]
transform = Transform3D(3, 0, 0, 0, 0.2, 0, 0, 0, 3, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="Pickups" type="Node3D" parent="."]

[node name="PistolPickup" parent="Pickups" instance=ExtResource("4_dtt05")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -33.5867, 15.3003, 2.01206)

[node name="ShotgunPickup" parent="Pickups" instance=ExtResource("5_2420q")]
transform = Transform3D(1, 0, 0, 0, 0.57472, -0.81835, 0, 0.81835, 0.57472, -18.8433, 28.8549, 25.0582)

[node name="PP8Pickup1" parent="Pickups" instance=ExtResource("6_pp8")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.338326, 17.38, 29.8781)

[node name="PP8Pickup2" parent="Pickups" instance=ExtResource("6_pp8")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.49601, 9.14779, 0.207466)

[node name="PP8Pickup3" parent="Pickups" instance=ExtResource("6_pp8")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 11.5523, 20)
