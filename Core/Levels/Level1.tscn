[gd_scene load_steps=14 format=3 uid="uid://b55rtaf8bv78"]

[ext_resource type="PackedScene" uid="uid://b3yxcv7jwaq8r" path="res://Core/Player/Player.tscn" id="1_player"]
[ext_resource type="PackedScene" uid="uid://c6k1du6osiry0" path="res://Core/UI/PauseMenu.tscn" id="2_pause"]
[ext_resource type="PackedScene" uid="uid://6k1du6osiry0" path="res://Core/Pickups/Resources/PistolPickup.tscn" id="3_pistol"]
[ext_resource type="PackedScene" uid="uid://b6k1du6osiry0" path="res://Core/Pickups/Resources/ShotgunPickup.tscn" id="4_shotgun"]
[ext_resource type="Script" uid="uid://dkuok504vr60e" path="res://Core/Utils/JumpPad.gd" id="5_jumppad"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_sky"]
sky_horizon_color = Color(0.64625, 0.65575, 0.67075, 1)
ground_horizon_color = Color(0.64625, 0.65575, 0.67075, 1)

[sub_resource type="Sky" id="Sky_sky"]
sky_material = SubResource("ProceduralSkyMaterial_sky")

[sub_resource type="Environment" id="Environment_env"]
background_mode = 2
sky = SubResource("Sky_sky")
tonemap_mode = 2
glow_enabled = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_floor"]
albedo_color = Color(0.2, 0.2, 0.2, 1)
metallic = 0.1
roughness = 0.8

[sub_resource type="BoxMesh" id="BoxMesh_platform"]
material = SubResource("StandardMaterial3D_floor")
size = Vector3(50, 1, 50)

[sub_resource type="BoxShape3D" id="BoxShape3D_platform"]
size = Vector3(50, 1, 50)

[sub_resource type="CylinderMesh" id="CylinderMesh_jumppad"]
top_radius = 2.0
bottom_radius = 2.0
height = 0.2

[sub_resource type="CylinderShape3D" id="CylinderShape3D_jumppad"]
height = 0.2
radius = 2.0

[node name="Level1" type="Node3D"]

[node name="Environment" type="Node3D" parent="."]

[node name="WorldEnvironment" type="WorldEnvironment" parent="Environment"]
environment = SubResource("Environment_env")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="Environment"]
transform = Transform3D(-0.866023, -0.433016, 0.250001, 0, 0.499998, 0.866027, -0.500003, 0.749999, -0.43301, 0, 0, 0)
shadow_enabled = true

[node name="Player" parent="." instance=ExtResource("1_player")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6.632, 0.9, 8.366)

[node name="PauseMenu" parent="." instance=ExtResource("2_pause")]
input_manager = NodePath("/root/InputManager")

[node name="Map" type="Node3D" parent="."]

[node name="Floor" type="StaticBody3D" parent="Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.5, 0)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/Floor"]
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/Floor"]
shape = SubResource("BoxShape3D_platform")

[node name="Platform1" type="StaticBody3D" parent="Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15, 2, -15)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/Platform1"]
transform = Transform3D(0.852656, 0, 0, 0, 0.2, 0, 0, 0, 0.5, 0, 0, 0)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/Platform1"]
transform = Transform3D(0.852656, 0, 0, 0, 0.2, 0, 0, 0, 0.5, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="Platform2" type="StaticBody3D" parent="Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 4, -15)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/Platform2"]
transform = Transform3D(0.5, 0, 0, 0, 0.2, 0, 0, 0, 0.5, 0, 0, 0)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/Platform2"]
transform = Transform3D(0.5, 0, 0, 0, 0.2, 0, 0, 0, 0.5, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="Platform3" type="StaticBody3D" parent="Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 6, -20)
collision_mask = 2

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/Platform3"]
transform = Transform3D(0.5, 0, 0, 0, 0.2, 0, 0, 0, 0.5, 0, 0, 0)
mesh = SubResource("BoxMesh_platform")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/Platform3"]
transform = Transform3D(0.5, 0, 0, 0, 0.2, 0, 0, 0, 0.5, 0, 0, 0)
shape = SubResource("BoxShape3D_platform")

[node name="JumpPad" type="Area3D" parent="Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.1, 15)
collision_mask = 2
script = ExtResource("5_jumppad")

[node name="MeshInstance3D" type="MeshInstance3D" parent="Map/JumpPad"]
mesh = SubResource("CylinderMesh_jumppad")
skeleton = NodePath("../..")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Map/JumpPad"]
shape = SubResource("CylinderShape3D_jumppad")

[node name="AudioStreamPlayer3D" type="AudioStreamPlayer3D" parent="Map/JumpPad"]

[node name="Pickups" type="Node3D" parent="."]

[node name="PistolPickup" parent="Pickups" instance=ExtResource("3_pistol")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5.02205, 2.7181, -5.01983)

[node name="ShotgunPickup" parent="Pickups" instance=ExtResource("4_shotgun")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5.06679, 5.19634, -5.14753)
