@tool
extends Control

## Weapon Manager Dock
## Main UI for managing weapons in the editor

const WeaponResource = preload("res://addons/weapon_manager/weapon_resource.gd")
const WeaponTemplates = preload("res://addons/weapon_manager/weapon_templates.gd")
const WeaponPickupGenerator = preload("res://addons/weapon_manager/weapon_pickup_generator.gd")
const WeaponIntegration = preload("res://addons/weapon_manager/weapon_integration.gd")

# UI Components
var main_container: VBoxContainer
var toolbar: HBoxContainer
var weapon_list: ItemList
var details_panel: VBoxContainer
var property_editor: VBoxContainer

# Buttons
var new_weapon_btn: Button
var duplicate_btn: Button
var delete_btn: Button
var save_btn: Button
var export_btn: Button
var generate_pickup_btn: Button
var validate_btn: Button

# Current weapon data
var current_weapon: WeaponResource
var weapons_data: Array[WeaponResource] = []
var weapons_directory: String = "res://Core/Weapons/Resources/"

func _init():
	name = "Weapons"
	custom_minimum_size = Vector2(280, 400)
	_setup_ui()
	_load_existing_weapons()

func _setup_ui():
	# Main container
	main_container = VBoxContainer.new()
	add_child(main_container)
	main_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	main_container.add_theme_constant_override("separation", 4)

	# Title
	var title = Label.new()
	title.text = "Weapon Manager"
	title.add_theme_font_size_override("font_size", 14)
	main_container.add_child(title)

	# Toolbar
	toolbar = HBoxContainer.new()
	toolbar.add_theme_constant_override("separation", 2)
	main_container.add_child(toolbar)
	
	new_weapon_btn = Button.new()
	new_weapon_btn.text = "New"
	new_weapon_btn.tooltip_text = "Create a new weapon"
	new_weapon_btn.custom_minimum_size.x = 50
	toolbar.add_child(new_weapon_btn)
	new_weapon_btn.pressed.connect(_on_new_weapon)

	duplicate_btn = Button.new()
	duplicate_btn.text = "📋"
	duplicate_btn.tooltip_text = "Duplicate selected weapon"
	duplicate_btn.disabled = true
	duplicate_btn.custom_minimum_size.x = 35
	toolbar.add_child(duplicate_btn)
	duplicate_btn.pressed.connect(_on_duplicate_weapon)

	delete_btn = Button.new()
	delete_btn.text = "🗑️"
	delete_btn.tooltip_text = "Delete selected weapon"
	delete_btn.disabled = true
	delete_btn.custom_minimum_size.x = 35
	toolbar.add_child(delete_btn)
	delete_btn.pressed.connect(_on_delete_weapon)

	# Add spacer
	var spacer = Control.new()
	spacer.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	toolbar.add_child(spacer)

	# Convert button for legacy weapon scenes (hidden by default)
	var convert_btn = Button.new()
	convert_btn.text = "⚙"
	convert_btn.tooltip_text = "Convert existing weapon scenes (one-time setup)"
	convert_btn.custom_minimum_size.x = 30
	convert_btn.visible = false  # Hidden unless needed
	toolbar.add_child(convert_btn)
	convert_btn.pressed.connect(_convert_existing_weapon_scenes)

	# Check if we need to show the convert button
	_check_for_legacy_weapons(convert_btn)
	
	# Weapon list
	var list_label = Label.new()
	list_label.text = "Weapons:"
	list_label.add_theme_font_size_override("font_size", 12)
	main_container.add_child(list_label)

	# Instructions
	var instructions = Label.new()
	instructions.text = "💡 Right-click for options • Double-click to add to scene\n🎯 Weapons are placed where camera is looking"
	instructions.add_theme_font_size_override("font_size", 9)
	instructions.add_theme_color_override("font_color", Color.GRAY)
	instructions.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	main_container.add_child(instructions)

	weapon_list = ItemList.new()
	weapon_list.custom_minimum_size = Vector2(0, 120)
	weapon_list.size_flags_vertical = Control.SIZE_EXPAND_FILL
	weapon_list.allow_reselect = true
	main_container.add_child(weapon_list)
	weapon_list.item_selected.connect(_on_weapon_selected)

	# Enable drag and drop
	_setup_drag_and_drop()

	# Details panel
	var details_label = Label.new()
	details_label.text = "Weapon Details:"
	details_label.add_theme_font_size_override("font_size", 12)
	main_container.add_child(details_label)

	var scroll_container = ScrollContainer.new()
	scroll_container.size_flags_vertical = Control.SIZE_EXPAND_FILL
	scroll_container.custom_minimum_size = Vector2(0, 150)
	main_container.add_child(scroll_container)

	details_panel = VBoxContainer.new()
	details_panel.add_theme_constant_override("separation", 2)
	details_panel.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	scroll_container.add_child(details_panel)
	
	# Bottom toolbar - use VBox for better layout
	var bottom_section = VBoxContainer.new()
	bottom_section.add_theme_constant_override("separation", 2)
	main_container.add_child(bottom_section)

	# First row of buttons
	var bottom_toolbar1 = HBoxContainer.new()
	bottom_toolbar1.add_theme_constant_override("separation", 2)
	bottom_section.add_child(bottom_toolbar1)

	save_btn = Button.new()
	save_btn.text = "Save"
	save_btn.tooltip_text = "Save current weapon"
	save_btn.disabled = true
	save_btn.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	bottom_toolbar1.add_child(save_btn)
	save_btn.pressed.connect(_on_save_weapon)

	export_btn = Button.new()
	export_btn.text = "Export"
	export_btn.tooltip_text = "Export weapon as scene file"
	export_btn.disabled = true
	export_btn.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	bottom_toolbar1.add_child(export_btn)
	export_btn.pressed.connect(_on_export_weapon)

	# Second row of buttons
	var bottom_toolbar2 = HBoxContainer.new()
	bottom_toolbar2.add_theme_constant_override("separation", 2)
	bottom_section.add_child(bottom_toolbar2)

	generate_pickup_btn = Button.new()
	generate_pickup_btn.text = "Pickup"
	generate_pickup_btn.tooltip_text = "Generate pickup scene for this weapon"
	generate_pickup_btn.disabled = true
	generate_pickup_btn.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	bottom_toolbar2.add_child(generate_pickup_btn)
	generate_pickup_btn.pressed.connect(_on_generate_pickup)

	validate_btn = Button.new()
	validate_btn.text = "Validate"
	validate_btn.tooltip_text = "Validate weapon configuration"
	validate_btn.disabled = true
	validate_btn.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	bottom_toolbar2.add_child(validate_btn)
	validate_btn.pressed.connect(_on_validate_weapon)

func _load_existing_weapons():
	# Don't clear existing weapons - only load if empty
	if weapons_data.size() > 0:
		return

	# Ensure weapons directory exists
	if not DirAccess.dir_exists_absolute(weapons_directory):
		DirAccess.open("res://").make_dir_recursive(weapons_directory)

	# Load existing weapon resources (.tres files)
	_load_weapon_resources()

	print("Loaded ", weapons_data.size(), " weapons")

## Check if there are legacy weapon scenes that need conversion
func _check_for_legacy_weapons(convert_btn: Button):
	var dir = DirAccess.open(weapons_directory)
	if not dir:
		return

	dir.list_dir_begin()
	var file_name = dir.get_next()
	var has_legacy_scenes = false

	while file_name != "":
		if file_name.ends_with(".tscn") and not file_name.contains("pickup"):
			has_legacy_scenes = true
			break
		file_name = dir.get_next()

	if has_legacy_scenes:
		convert_btn.visible = true
		print("Legacy weapon scenes found - conversion button enabled")

func _load_weapon_resources():
	var dir = DirAccess.open(weapons_directory)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		while file_name != "":
			if file_name.ends_with(".tres") and file_name.begins_with("weapon_"):
				var resource_path = weapons_directory + file_name
				var weapon_resource = load(resource_path) as WeaponResource
				if weapon_resource:
					# Ensure weapon has valid data
					if weapon_resource.weapon_name != "":
						weapons_data.append(weapon_resource)
						weapon_list.add_item(weapon_resource.weapon_name)
						print("Loaded weapon: ", weapon_resource.weapon_name)
					else:
						print("Warning: Weapon resource has empty name: ", resource_path)
				else:
					print("Warning: Failed to load weapon resource: ", resource_path)
			file_name = dir.get_next()
		dir.list_dir_end()

func _convert_existing_weapon_scenes():
	var dir = DirAccess.open(weapons_directory)
	if not dir:
		print("Could not open weapons directory: ", weapons_directory)
		return

	dir.list_dir_begin()
	var file_name = dir.get_next()

	while file_name != "":
		if file_name.ends_with(".tscn") and not file_name.contains("pickup"):
			var scene_path = weapons_directory + file_name
			print("Attempting to convert weapon scene: ", scene_path)

			var scene = load(scene_path) as PackedScene
			if scene:
				var weapon_node = scene.instantiate()
				if weapon_node:
					print("Instantiated weapon node: ", weapon_node.name)

					# Check if it has weapon properties
					if "weapon_name" in weapon_node:
						var weapon_name = weapon_node.weapon_name
						print("Found weapon: ", weapon_name)

						# Check if we already have this weapon as a resource
						var already_exists = false
						for existing_weapon in weapons_data:
							if existing_weapon.weapon_name == weapon_name:
								already_exists = true
								break

						if not already_exists:
							print("Converting weapon: ", weapon_name)
							var weapon_resource = _create_weapon_resource_from_scene(weapon_node)
							if weapon_resource:
								print("Weapon resource created successfully")
								weapons_data.append(weapon_resource)
								weapon_list.add_item(weapon_resource.weapon_name)

								# Save the converted resource
								var resource_filename = "weapon_" + weapon_resource.weapon_name.to_lower().replace(" ", "_") + ".tres"
								var resource_path = weapons_directory + resource_filename
								print("Saving to: ", resource_path)
								var save_result = ResourceSaver.save(weapon_resource, resource_path)
								if save_result == OK:
									print("✓ Converted weapon scene to resource: ", weapon_resource.weapon_name)
								else:
									print("✗ Failed to save converted weapon: ", weapon_resource.weapon_name, " Error: ", save_result)
							else:
								print("✗ Failed to create weapon resource for: ", weapon_name)
						else:
							print("Weapon already exists as resource: ", weapon_name)
					else:
						print("Scene does not appear to be a weapon: ", file_name)

					weapon_node.queue_free()
				else:
					print("Failed to instantiate scene: ", scene_path)
			else:
				print("Failed to load scene: ", scene_path)

		file_name = dir.get_next()

func _create_weapon_resource_from_scene(weapon_node) -> WeaponResource:
	print("Creating weapon resource from scene node: ", weapon_node.name)

	# Try to create WeaponResource with multiple fallback methods
	var weapon_resource = null

	# Method 1: Try ClassDB
	if ClassDB.class_exists("WeaponResource"):
		weapon_resource = ClassDB.instantiate("WeaponResource")
		print("Created WeaponResource via ClassDB")

	# Method 2: Try direct script loading
	if not weapon_resource:
		var script = load("res://addons/weapon_manager/weapon_resource.gd")
		if script:
			weapon_resource = script.new()
			print("Created WeaponResource via script loading")

	# Method 3: Try preload
	if not weapon_resource:
		var WeaponResourceScript = preload("res://addons/weapon_manager/weapon_resource.gd")
		if WeaponResourceScript:
			weapon_resource = WeaponResourceScript.new()
			print("Created WeaponResource via preload")

	if not weapon_resource:
		print("Failed to create WeaponResource - all methods failed")
		return null

	print("WeaponResource created successfully")

	# Copy basic properties with simple direct access
	weapon_resource.weapon_name = weapon_node.weapon_name
	weapon_resource.weapon_description = weapon_node.weapon_description if "weapon_description" in weapon_node else "Converted weapon"
	weapon_resource.damage = weapon_node.damage
	weapon_resource.fire_rate = weapon_node.fire_rate
	weapon_resource.reload_time = weapon_node.reload_time
	weapon_resource.ammo_per_clip = weapon_node.ammo_per_clip
	weapon_resource.max_ammo = weapon_node.max_ammo
	weapon_resource.auto_fire = weapon_node.auto_fire if "auto_fire" in weapon_node else false
	weapon_resource.spread = weapon_node.spread

	# Set weapon type based on name
	var name_lower = weapon_resource.weapon_name.to_lower()
	if "pistol" in name_lower or "pp8" in name_lower:
		weapon_resource.weapon_type = "Pistol"
		weapon_resource.is_dual_wieldable = true
	elif "shotgun" in name_lower:
		weapon_resource.weapon_type = "Shotgun"
	elif "rifle" in name_lower:
		weapon_resource.weapon_type = "Rifle"
	elif "smg" in name_lower:
		weapon_resource.weapon_type = "SMG"
	else:
		weapon_resource.weapon_type = "Pistol"

	# Copy audio and effects if available
	weapon_resource.fire_sound = weapon_node.fire_sound if "fire_sound" in weapon_node else null
	weapon_resource.reload_sound = weapon_node.reload_sound if "reload_sound" in weapon_node else null
	weapon_resource.empty_sound = weapon_node.empty_sound if "empty_sound" in weapon_node else null
	weapon_resource.equip_sound = weapon_node.equip_sound if "equip_sound" in weapon_node else null
	weapon_resource.muzzle_flash = weapon_node.muzzle_flash if "muzzle_flash" in weapon_node else null
	weapon_resource.impact_effect = weapon_node.impact_effect if "impact_effect" in weapon_node else null

	# Copy animation names
	weapon_resource.fire_animation = weapon_node.fire_animation if "fire_animation" in weapon_node else "fire"
	weapon_resource.reload_animation = weapon_node.reload_animation if "reload_animation" in weapon_node else "reload"
	weapon_resource.equip_animation = weapon_node.equip_animation if "equip_animation" in weapon_node else "equip"

	weapon_resource.template_used = "Converted from scene"

	return weapon_resource

func _on_new_weapon():
	var template_dialog = _create_template_dialog()
	add_child(template_dialog)
	template_dialog.popup_centered(Vector2i(400, 300))

func _create_template_dialog() -> Window:
	var dialog = Window.new()
	dialog.title = "Create New Weapon"
	dialog.size = Vector2i(450, 400)
	dialog.unresizable = false

	var vbox = VBoxContainer.new()
	vbox.add_theme_constant_override("separation", 8)
	dialog.add_child(vbox)
	vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	vbox.add_theme_constant_override("margin_left", 16)
	vbox.add_theme_constant_override("margin_right", 16)
	vbox.add_theme_constant_override("margin_top", 16)
	vbox.add_theme_constant_override("margin_bottom", 16)

	# Weapon name input
	var name_label = Label.new()
	name_label.text = "Weapon Name:"
	vbox.add_child(name_label)

	var name_input = LineEdit.new()
	name_input.placeholder_text = "Enter unique weapon name..."
	vbox.add_child(name_input)

	# Template selection
	var template_label = Label.new()
	template_label.text = "Select Template:"
	vbox.add_child(template_label)

	var template_list = ItemList.new()
	template_list.custom_minimum_size = Vector2(400, 200)
	vbox.add_child(template_list)

	# Add templates
	var templates = WeaponTemplates.get_available_templates()
	for template in templates:
		template_list.add_item(template.name + " - " + template.description)

	# Buttons
	var button_container = HBoxContainer.new()
	vbox.add_child(button_container)

	var cancel_btn = Button.new()
	cancel_btn.text = "Cancel"
	button_container.add_child(cancel_btn)

	button_container.add_child(Control.new())  # Spacer
	button_container.get_child(-1).size_flags_horizontal = Control.SIZE_EXPAND_FILL

	var create_btn = Button.new()
	create_btn.text = "Create Weapon"
	create_btn.disabled = true  # Disabled until valid input
	button_container.add_child(create_btn)

	# Validation function
	var validate_input = func():
		var name_valid = name_input.text.strip_edges() != "" and _is_weapon_name_unique(name_input.text.strip_edges())
		var template_valid = template_list.get_selected_items().size() > 0
		create_btn.disabled = not (name_valid and template_valid)

	# Connect validation
	name_input.text_changed.connect(func(_text): validate_input.call())
	template_list.item_selected.connect(func(_index): validate_input.call())

	# Connect buttons
	cancel_btn.pressed.connect(func(): dialog.queue_free())
	create_btn.pressed.connect(func():
		var selected = template_list.get_selected_items()
		if selected.size() > 0 and name_input.text.strip_edges() != "":
			var template = templates[selected[0]]
			var weapon_name = name_input.text.strip_edges()
			_create_weapon_from_template(template, weapon_name)
		dialog.queue_free()
	)

	return dialog

## Check if weapon name is unique
func _is_weapon_name_unique(weapon_name: String) -> bool:
	for weapon in weapons_data:
		if weapon.weapon_name == weapon_name:
			return false
	return true

func _create_weapon_from_template(template: Dictionary, weapon_name: String = ""):
	var new_weapon = WeaponResource.new()

	# Use provided name or template name
	var final_name = weapon_name if weapon_name != "" else template.get("name", "New Weapon")

	# Apply template data
	new_weapon.weapon_name = final_name
	new_weapon.weapon_description = template.get("description", "A new weapon")
	new_weapon.damage = template.get("damage", 10.0)
	new_weapon.fire_rate = template.get("fire_rate", 1.0)
	new_weapon.reload_time = template.get("reload_time", 1.5)
	new_weapon.ammo_per_clip = template.get("ammo_per_clip", 10)
	new_weapon.max_ammo = template.get("max_ammo", 100)
	new_weapon.auto_fire = template.get("auto_fire", false)
	new_weapon.spread = template.get("spread", 0.0)
	new_weapon.weapon_type = template.get("weapon_type", "Pistol")
	new_weapon.is_dual_wieldable = template.get("is_dual_wieldable", false)
	new_weapon.template_used = template.get("name", "")

	# Add to list
	weapons_data.append(new_weapon)
	weapon_list.add_item(new_weapon.weapon_name)

	# Select the new weapon
	weapon_list.select(weapons_data.size() - 1)
	_on_weapon_selected(weapons_data.size() - 1)

	# Auto-save the new weapon
	_auto_save_weapon(new_weapon)

## Generate unique weapon name for duplicates
func _generate_unique_weapon_name(base_name: String) -> String:
	var unique_name = base_name
	var counter = 1

	while not _is_weapon_name_unique(unique_name):
		unique_name = base_name + "_" + str(counter)
		counter += 1

	return unique_name

## Auto-save weapon to disk
func _auto_save_weapon(weapon: WeaponResource):
	if not weapon:
		return

	# Generate filename
	var filename = "weapon_" + weapon.weapon_name.to_lower().replace(" ", "_") + ".tres"
	var save_path = weapons_directory + filename

	# Save the resource
	var result = ResourceSaver.save(weapon, save_path)
	if result == OK:
		print("Auto-saved weapon: ", save_path)
	else:
		print("Failed to auto-save weapon: ", result)

func _on_duplicate_weapon():
	if current_weapon == null:
		return

	var duplicated = current_weapon.duplicate()

	# Generate unique name
	var base_name = current_weapon.weapon_name + "_Copy"
	duplicated.weapon_name = _generate_unique_weapon_name(base_name)

	duplicated.created_date = Time.get_datetime_string_from_system()
	duplicated.update_modified_time()

	weapons_data.append(duplicated)
	weapon_list.add_item(duplicated.weapon_name)

	# Select the duplicated weapon
	weapon_list.select(weapons_data.size() - 1)
	_on_weapon_selected(weapons_data.size() - 1)

	# Auto-save the duplicated weapon
	_auto_save_weapon(duplicated)

func _on_delete_weapon():
	if current_weapon == null:
		return

	var selected_index = weapon_list.get_selected_items()[0]
	var weapon_to_delete = current_weapon

	# Confirm deletion
	var confirm_dialog = ConfirmationDialog.new()
	confirm_dialog.dialog_text = "Are you sure you want to delete '" + weapon_to_delete.weapon_name + "'?\n\nThis will permanently remove the weapon file from disk."
	add_child(confirm_dialog)

	confirm_dialog.confirmed.connect(func():
		# Delete weapon file from disk
		var filename = "weapon_" + weapon_to_delete.weapon_name.to_lower().replace(" ", "_") + ".tres"
		var file_path = weapons_directory + filename

		if FileAccess.file_exists(file_path):
			DirAccess.open("res://").remove(file_path)
			print("Deleted weapon file: ", file_path)

		# Remove from data
		weapons_data.remove_at(selected_index)
		weapon_list.remove_item(selected_index)

		# Clear current selection
		current_weapon = null
		_update_details_panel()
		_update_button_states()

		confirm_dialog.queue_free()
	)

	confirm_dialog.popup_centered()

func _on_weapon_selected(index: int):
	if index >= 0 and index < weapons_data.size():
		current_weapon = weapons_data[index]
		_update_details_panel()
		_update_button_states()

func _update_button_states():
	var has_selection = current_weapon != null
	duplicate_btn.disabled = not has_selection
	delete_btn.disabled = not has_selection
	save_btn.disabled = not has_selection
	export_btn.disabled = not has_selection
	generate_pickup_btn.disabled = not has_selection
	validate_btn.disabled = not has_selection

func _update_details_panel():
	# Clear existing controls
	for child in details_panel.get_children():
		child.queue_free()

	if current_weapon == null:
		var no_selection = Label.new()
		no_selection.text = "No weapon selected"
		details_panel.add_child(no_selection)
		return

	# Create property editors
	_create_property_editor("Name", "weapon_name", "string")
	_create_property_editor("Description", "weapon_description", "string")
	_create_property_editor("Type", "weapon_type", "enum")

	var separator1 = HSeparator.new()
	details_panel.add_child(separator1)

	_create_property_editor("Damage", "damage", "float")
	_create_property_editor("Fire Rate", "fire_rate", "float")
	_create_property_editor("Reload Time", "reload_time", "float")
	_create_property_editor("Ammo per Clip", "ammo_per_clip", "int")
	_create_property_editor("Max Ammo", "max_ammo", "int")
	_create_property_editor("Auto Fire", "auto_fire", "bool")
	_create_property_editor("Spread", "spread", "float")

	var separator2 = HSeparator.new()
	details_panel.add_child(separator2)

	_create_property_editor("Dual Wieldable", "is_dual_wieldable", "bool")
	_create_property_editor("Recoil Strength", "recoil_strength", "float")
	_create_property_editor("Max Range", "range_max", "float")

	# Model import section
	var separator3 = HSeparator.new()
	details_panel.add_child(separator3)

	var model_section = VBoxContainer.new()
	model_section.add_theme_constant_override("separation", 2)
	details_panel.add_child(model_section)

	var model_label = Label.new()
	model_label.text = "3D Model:"
	model_label.add_theme_font_size_override("font_size", 11)
	model_section.add_child(model_label)

	var model_path_label = Label.new()
	model_path_label.text = "No model" if current_weapon.weapon_model == null else str(current_weapon.weapon_model.resource_path).get_file()
	model_path_label.add_theme_font_size_override("font_size", 10)
	model_path_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	model_section.add_child(model_path_label)

	var browse_model_btn = Button.new()
	browse_model_btn.text = "Browse Model"
	browse_model_btn.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	model_section.add_child(browse_model_btn)
	browse_model_btn.pressed.connect(_on_browse_model)

func _create_property_editor(label_text: String, property_name: String, property_type: String):
	var container = VBoxContainer.new()
	container.add_theme_constant_override("separation", 1)
	details_panel.add_child(container)

	var label = Label.new()
	label.text = label_text + ":"
	label.add_theme_font_size_override("font_size", 11)
	container.add_child(label)

	var editor_control: Control

	match property_type:
		"string":
			var line_edit = LineEdit.new()
			line_edit.text = str(current_weapon.get(property_name))
			line_edit.size_flags_horizontal = Control.SIZE_EXPAND_FILL
			line_edit.text_changed.connect(func(new_text): _on_property_changed(property_name, new_text))
			editor_control = line_edit

		"int":
			var spin_box = SpinBox.new()
			spin_box.value = current_weapon.get(property_name)
			spin_box.step = 1
			spin_box.allow_greater = true
			spin_box.allow_lesser = true
			spin_box.size_flags_horizontal = Control.SIZE_EXPAND_FILL
			spin_box.value_changed.connect(func(new_value): _on_property_changed(property_name, int(new_value)))
			editor_control = spin_box

		"float":
			var spin_box = SpinBox.new()
			spin_box.value = current_weapon.get(property_name)
			spin_box.step = 0.1
			spin_box.allow_greater = true
			spin_box.allow_lesser = true
			spin_box.size_flags_horizontal = Control.SIZE_EXPAND_FILL
			spin_box.value_changed.connect(func(new_value): _on_property_changed(property_name, new_value))
			editor_control = spin_box

		"bool":
			var check_box = CheckBox.new()
			check_box.button_pressed = current_weapon.get(property_name)
			check_box.toggled.connect(func(pressed): _on_property_changed(property_name, pressed))
			editor_control = check_box

		"enum":
			var option_button = OptionButton.new()
			option_button.size_flags_horizontal = Control.SIZE_EXPAND_FILL
			var weapon_types = ["Pistol", "Rifle", "Shotgun", "SMG", "Sniper", "Heavy", "Special"]
			for type in weapon_types:
				option_button.add_item(type)
			var current_type = current_weapon.get(property_name)
			var type_index = weapon_types.find(current_type)
			if type_index >= 0:
				option_button.selected = type_index
			option_button.item_selected.connect(func(index): _on_property_changed(property_name, weapon_types[index]))
			editor_control = option_button

	if editor_control:
		container.add_child(editor_control)

func _on_property_changed(property_name: String, new_value):
	if current_weapon:
		current_weapon.set(property_name, new_value)
		current_weapon.update_modified_time()

		# Update weapon name in list if name changed
		if property_name == "weapon_name":
			var selected_index = weapon_list.get_selected_items()[0]
			weapon_list.set_item_text(selected_index, new_value)

		# Auto-save when properties change
		_auto_save_weapon(current_weapon)

func _on_browse_model():
	var file_dialog = FileDialog.new()
	file_dialog.file_mode = FileDialog.FILE_MODE_OPEN_FILE
	file_dialog.access = FileDialog.ACCESS_RESOURCES
	file_dialog.add_filter("*.tscn", "Scene Files")
	file_dialog.add_filter("*.glb", "GLTF Binary")
	file_dialog.add_filter("*.gltf", "GLTF Text")
	file_dialog.add_filter("*.obj", "Wavefront OBJ")
	file_dialog.add_filter("*.fbx", "FBX")

	add_child(file_dialog)
	file_dialog.file_selected.connect(_on_model_selected)
	file_dialog.popup_centered(Vector2i(800, 600))

func _on_model_selected(path: String):
	if current_weapon:
		if path.ends_with(".tscn"):
			current_weapon.weapon_model = load(path)
		else:
			# For 3D model files, we need to import them as scenes
			print("Model file selected: ", path)
			# TODO: Handle 3D model import and conversion to scene

		_update_details_panel()

	# Clean up the file dialog
	var file_dialog = get_children().filter(func(child): return child is FileDialog)
	for dialog in file_dialog:
		dialog.queue_free()

func _on_save_weapon():
	if current_weapon == null:
		return

	# Generate filename
	var filename = "weapon_" + current_weapon.weapon_name.to_lower().replace(" ", "_") + ".tres"
	var save_path = weapons_directory + filename

	# Save the resource
	var result = ResourceSaver.save(current_weapon, save_path)
	if result == OK:
		print("Weapon saved: ", save_path)
		# Show success message
		var dialog = AcceptDialog.new()
		dialog.dialog_text = "Weapon saved successfully!"
		add_child(dialog)
		dialog.popup_centered()
		dialog.confirmed.connect(func(): dialog.queue_free())
	else:
		print("Failed to save weapon: ", result)

func _on_export_weapon():
	if current_weapon == null:
		return

	# Generate weapon scene
	var weapon_scene = current_weapon.generate_weapon_scene()

	# Save scene
	var scene_filename = current_weapon.weapon_name.to_lower().replace(" ", "_") + ".tscn"
	var scene_path = weapons_directory + scene_filename

	var result = ResourceSaver.save(weapon_scene, scene_path)
	if result == OK:
		print("Weapon scene exported: ", scene_path)
		# Show success message
		var dialog = AcceptDialog.new()
		dialog.dialog_text = "Weapon scene exported successfully!\nPath: " + scene_path
		add_child(dialog)
		dialog.popup_centered()
		dialog.confirmed.connect(func(): dialog.queue_free())
	else:
		print("Failed to export weapon scene: ", result)

func _on_generate_pickup():
	if current_weapon == null:
		return

	# Generate pickup scene
	var pickup_scene = WeaponPickupGenerator.generate_pickup_scene(current_weapon)

	# Save pickup scene
	var pickup_filename = current_weapon.weapon_name.to_lower().replace(" ", "_") + "_pickup.tscn"
	var pickup_path = "res://Core/Pickups/" + pickup_filename

	var result = ResourceSaver.save(pickup_scene, pickup_path)
	if result == OK:
		print("Weapon pickup generated: ", pickup_path)
		# Show success message
		var dialog = AcceptDialog.new()
		dialog.dialog_text = "Weapon pickup generated successfully!\nPath: " + pickup_path
		add_child(dialog)
		dialog.popup_centered()
		dialog.confirmed.connect(func(): dialog.queue_free())
	else:
		print("Failed to generate weapon pickup: ", result)

func _on_validate_weapon():
	if current_weapon == null:
		return

	var issues = WeaponIntegration.validate_weapon_resource(current_weapon)

	var dialog = AcceptDialog.new()
	if issues.is_empty():
		dialog.dialog_text = "Weapon validation passed!\nNo issues found."
		dialog.title = "Validation Success"
	else:
		dialog.dialog_text = "Weapon validation failed!\n\nIssues found:\n• " + "\n• ".join(issues)
		dialog.title = "Validation Failed"

	add_child(dialog)
	dialog.popup_centered()
	dialog.confirmed.connect(func(): dialog.queue_free())

func _setup_drag_and_drop():
	# Connect context menu and double-click
	weapon_list.item_clicked.connect(_on_weapon_list_item_clicked)
	weapon_list.item_activated.connect(_on_weapon_list_item_activated)

	# Enable drag and drop
	weapon_list.set_drag_forwarding(_can_drop_data_fw, _drop_data_fw, _get_drag_data_fw)

func _on_weapon_list_item_activated(index: int):
	# Double-click to add to scene
	if index < weapons_data.size():
		var weapon_resource = weapons_data[index]
		_create_pickup_in_scene(weapon_resource)

## Drag and drop implementation
func _get_drag_data_fw(position: Vector2):
	var selected_items = weapon_list.get_selected_items()
	if selected_items.is_empty():
		return null

	var index = selected_items[0]
	if index >= weapons_data.size():
		return null

	var weapon_resource = weapons_data[index]

	# Create drag preview
	var preview = Label.new()
	preview.text = "🔫 " + weapon_resource.weapon_name
	preview.add_theme_color_override("font_color", Color.WHITE)
	preview.add_theme_color_override("font_shadow_color", Color.BLACK)
	preview.add_theme_constant_override("shadow_offset_x", 1)
	preview.add_theme_constant_override("shadow_offset_y", 1)

	weapon_list.set_drag_preview(preview)

	# Return drag data
	return {
		"type": "weapon_pickup",
		"weapon_resource": weapon_resource,
		"source": "weapon_manager"
	}

func _can_drop_data_fw(position: Vector2, data) -> bool:
	# We don't handle drops on the weapon list itself
	return false

func _drop_data_fw(position: Vector2, data):
	# We don't handle drops on the weapon list itself
	pass

func _create_pickup_in_scene(weapon_resource: WeaponResource):
	print("Creating pickup in scene for: ", weapon_resource.weapon_name)

	var edited_scene = EditorInterface.get_edited_scene_root()
	if not edited_scene:
		print("No scene to drop weapon into")
		_show_error_dialog("No scene is currently open for editing.")
		return

	print("Target scene: ", edited_scene.name)

	# Generate pickup scene
	var pickup_scene = WeaponPickupGenerator.generate_pickup_scene(weapon_resource)
	if not pickup_scene:
		print("Failed to generate pickup scene")
		_show_error_dialog("Failed to generate pickup scene for " + weapon_resource.weapon_name)
		return

	# Instantiate the pickup
	var pickup_instance = pickup_scene.instantiate()
	if not pickup_instance:
		print("Failed to instantiate pickup scene")
		_show_error_dialog("Failed to instantiate pickup scene")
		return

	pickup_instance.name = weapon_resource.weapon_name + "_Pickup"
	print("Pickup instance created: ", pickup_instance.name)

	# Add to the scene
	edited_scene.add_child(pickup_instance)
	pickup_instance.owner = edited_scene

	# Position it based on camera position and direction
	if pickup_instance is Node3D:
		var placement_position = _get_camera_placement_position()
		pickup_instance.position = placement_position
		print("Pickup positioned at: ", pickup_instance.position)

	print("✓ Added weapon pickup to scene: ", weapon_resource.weapon_name)

	# Add visual feedback - briefly highlight the placed object
	_highlight_placed_object(pickup_instance)

	# Show success message
	var dialog = AcceptDialog.new()
	dialog.dialog_text = "Weapon pickup added to scene!\n\nWeapon: " + weapon_resource.weapon_name + "\nPosition: " + str(pickup_instance.position) + "\nScene: " + edited_scene.name
	add_child(dialog)
	dialog.popup_centered()
	dialog.confirmed.connect(func(): dialog.queue_free())

## Get placement position based on 3D editor camera
func _get_camera_placement_position() -> Vector3:
	# Try to get the 3D editor viewport and camera
	var viewport_3d = EditorInterface.get_editor_viewport_3d(0)
	if not viewport_3d:
		print("Warning: Could not get 3D viewport, using default position")
		return Vector3(0, 1, 0)

	var camera = viewport_3d.get_camera_3d()
	if not camera:
		print("Warning: Could not get 3D camera, using default position")
		return Vector3(0, 1, 0)

	# Ensure we have a valid world
	if not viewport_3d.world_3d:
		print("Warning: No 3D world available, using camera-relative position")
		var camera_transform = camera.get_camera_transform()
		return camera_transform.origin + (-camera_transform.basis.z * 10.0)

	# Get camera transform
	var camera_transform = camera.get_camera_transform()
	var camera_position = camera_transform.origin
	var camera_forward = -camera_transform.basis.z  # Camera looks down negative Z

	# Cast a ray from camera to find placement position
	var space_state = viewport_3d.world_3d.direct_space_state
	if space_state:
		var query = PhysicsRayQueryParameters3D.create(
			camera_position,
			camera_position + camera_forward * 50.0  # Cast 50 units forward
		)
		var result = space_state.intersect_ray(query)

		if result:
			# Place slightly above the hit point
			return result.position + Vector3(0, 0.5, 0)
		else:
			# No collision, place at a reasonable distance from camera
			return camera_position + camera_forward * 10.0
	else:
		# Fallback: place in front of camera
		return camera_position + camera_forward * 10.0

## Get placement position from screen coordinates (for drag-and-drop)
func _get_placement_position_from_screen(screen_pos: Vector2, viewport_3d: SubViewport) -> Vector3:
	var camera = viewport_3d.get_camera_3d()
	if not camera:
		return Vector3(0, 1, 0)

	# Project screen position to 3D world
	var from = camera.project_ray_origin(screen_pos)
	var to = from + camera.project_ray_normal(screen_pos) * 50.0

	# Cast ray to find intersection
	var space_state = viewport_3d.world_3d.direct_space_state
	if space_state:
		var query = PhysicsRayQueryParameters3D.create(from, to)
		var result = space_state.intersect_ray(query)

		if result:
			return result.position + Vector3(0, 0.5, 0)
		else:
			# No collision, place at reasonable distance
			return from + camera.project_ray_normal(screen_pos) * 10.0
	else:
		return from + camera.project_ray_normal(screen_pos) * 10.0

## Add visual feedback when placing objects
func _highlight_placed_object(node: Node3D):
	if not node:
		return

	# Create a temporary highlight effect
	var tween = create_tween()
	var original_scale = node.scale

	# Scale animation for visual feedback
	tween.tween_method(
		func(scale_factor: float):
			if is_instance_valid(node):
				node.scale = original_scale * scale_factor,
		1.0,
		1.2,
		0.2
	)
	tween.tween_method(
		func(scale_factor: float):
			if is_instance_valid(node):
				node.scale = original_scale * scale_factor,
		1.2,
		1.0,
		0.2
	)

	# Cleanup tween
	tween.finished.connect(func(): tween.kill())

## Create pickup with precise positioning (for drag-and-drop)
func _create_pickup_at_position(weapon_resource: WeaponResource, position: Vector3):
	var edited_scene = EditorInterface.get_edited_scene_root()
	if not edited_scene:
		_show_error_dialog("No scene is currently open for editing.")
		return

	# Generate pickup scene
	var pickup_scene = WeaponPickupGenerator.generate_pickup_scene(weapon_resource)
	if not pickup_scene:
		_show_error_dialog("Failed to generate pickup scene for " + weapon_resource.weapon_name)
		return

	# Instantiate the pickup
	var pickup_instance = pickup_scene.instantiate()
	if not pickup_instance:
		_show_error_dialog("Failed to instantiate pickup scene")
		return

	pickup_instance.name = weapon_resource.weapon_name + "_Pickup"

	# Add to the scene
	edited_scene.add_child(pickup_instance)
	pickup_instance.owner = edited_scene

	# Set precise position
	if pickup_instance is Node3D:
		pickup_instance.position = position

	# Add visual feedback
	_highlight_placed_object(pickup_instance)

	print("✓ Added weapon pickup at precise position: ", weapon_resource.weapon_name, " at ", position)

func _show_error_dialog(message: String):
	var dialog = AcceptDialog.new()
	dialog.dialog_text = "Error: " + message
	dialog.title = "Weapon Manager Error"
	add_child(dialog)
	dialog.popup_centered()
	dialog.confirmed.connect(func(): dialog.queue_free())

func _on_weapon_list_item_clicked(index: int, at_position: Vector2, mouse_button_index: int):
	if mouse_button_index == MOUSE_BUTTON_RIGHT and index < weapons_data.size():
		_show_weapon_context_menu(index, at_position)

func _show_weapon_context_menu(weapon_index: int, position: Vector2):
	var weapon_resource = weapons_data[weapon_index]

	var context_menu = PopupMenu.new()
	add_child(context_menu)

	context_menu.add_item("📋 Edit Properties", 0)
	context_menu.add_item("🔫 Add to Scene", 1)
	context_menu.add_item("📦 Generate Pickup", 2)
	context_menu.add_item("💾 Export Scene", 3)
	context_menu.add_separator()
	context_menu.add_item("🗑️ Delete", 4)

	context_menu.id_pressed.connect(func(id):
		_handle_context_menu_action(weapon_index, id)
		context_menu.queue_free()
	)

	# Position the menu at the click location
	var global_pos = weapon_list.global_position + position
	context_menu.popup(Rect2i(global_pos, Vector2i(150, 100)))

func _handle_context_menu_action(weapon_index: int, action_id: int):
	var weapon_resource = weapons_data[weapon_index]

	match action_id:
		0: # Edit Properties
			weapon_list.select(weapon_index)
			_on_weapon_selected(weapon_index)
		1: # Add to Scene
			_create_pickup_in_scene(weapon_resource)
		2: # Generate Pickup
			_generate_pickup_for_weapon(weapon_resource)
		3: # Export Scene
			current_weapon = weapon_resource
			_on_export_weapon()
		4: # Delete
			weapon_list.select(weapon_index)
			_on_weapon_selected(weapon_index)
			_on_delete_weapon()

func _generate_pickup_for_weapon(weapon_resource: WeaponResource):
	# Generate pickup scene
	var pickup_scene = WeaponPickupGenerator.generate_pickup_scene(weapon_resource)

	# Save pickup scene
	var pickup_filename = weapon_resource.weapon_name.to_lower().replace(" ", "_") + "_pickup.tscn"
	var pickup_path = "res://Core/Pickups/" + pickup_filename

	var result = ResourceSaver.save(pickup_scene, pickup_path)
	if result == OK:
		print("Weapon pickup generated: ", pickup_path)
		# Show success message
		var dialog = AcceptDialog.new()
		dialog.dialog_text = "Weapon pickup generated successfully!\nPath: " + pickup_path
		add_child(dialog)
		dialog.popup_centered()
		dialog.confirmed.connect(func(): dialog.queue_free())
	else:
		print("Failed to generate weapon pickup: ", result)
