@tool
extends RefCounted
class_name WeaponTemplates

## Weapon Templates
## Provides predefined weapon templates for quick weapon creation

static func get_available_templates() -> Array[Dictionary]:
	return [
		{
			"name": "Basic Pistol",
			"description": "Standard sidearm with moderate damage",
			"damage": 25.0,
			"fire_rate": 2.0,
			"reload_time": 1.0,
			"ammo_per_clip": 12,
			"max_ammo": 60,
			"auto_fire": false,
			"spread": 2.0,
			"weapon_type": "Pistol",
			"is_dual_wieldable": true,
			"recoil_strength": 0.5,
			"range_max": 500.0
		},
		{
			"name": "Assault Rifle",
			"description": "Versatile automatic weapon",
			"damage": 30.0,
			"fire_rate": 8.0,
			"reload_time": 2.0,
			"ammo_per_clip": 30,
			"max_ammo": 180,
			"auto_fire": true,
			"spread": 3.0,
			"weapon_type": "Rifle",
			"is_dual_wieldable": false,
			"recoil_strength": 1.2,
			"range_max": 800.0
		},
		{
			"name": "Combat Shotgun",
			"description": "High damage close-range weapon",
			"damage": 80.0,
			"fire_rate": 1.2,
			"reload_time": 2.5,
			"ammo_per_clip": 8,
			"max_ammo": 32,
			"auto_fire": false,
			"spread": 15.0,
			"weapon_type": "Shotgun",
			"is_dual_wieldable": false,
			"recoil_strength": 2.0,
			"range_max": 200.0
		},
		{
			"name": "SMG",
			"description": "Fast-firing submachine gun",
			"damage": 18.0,
			"fire_rate": 12.0,
			"reload_time": 1.5,
			"ammo_per_clip": 25,
			"max_ammo": 150,
			"auto_fire": true,
			"spread": 5.0,
			"weapon_type": "SMG",
			"is_dual_wieldable": true,
			"recoil_strength": 0.8,
			"range_max": 300.0
		},
		{
			"name": "Sniper Rifle",
			"description": "High-precision long-range weapon",
			"damage": 120.0,
			"fire_rate": 0.5,
			"reload_time": 3.0,
			"ammo_per_clip": 5,
			"max_ammo": 25,
			"auto_fire": false,
			"spread": 0.1,
			"weapon_type": "Sniper",
			"is_dual_wieldable": false,
			"recoil_strength": 3.0,
			"range_max": 2000.0
		},
		{
			"name": "Heavy Machine Gun",
			"description": "High damage automatic weapon",
			"damage": 45.0,
			"fire_rate": 6.0,
			"reload_time": 4.0,
			"ammo_per_clip": 100,
			"max_ammo": 400,
			"auto_fire": true,
			"spread": 8.0,
			"weapon_type": "Heavy",
			"is_dual_wieldable": false,
			"recoil_strength": 2.5,
			"range_max": 1000.0
		},
		{
			"name": "Rocket Launcher",
			"description": "Explosive projectile weapon",
			"damage": 200.0,
			"fire_rate": 0.3,
			"reload_time": 5.0,
			"ammo_per_clip": 1,
			"max_ammo": 10,
			"auto_fire": false,
			"spread": 0.0,
			"weapon_type": "Heavy",
			"is_dual_wieldable": false,
			"recoil_strength": 4.0,
			"range_max": 1500.0
		},
		{
			"name": "Energy Weapon",
			"description": "Futuristic energy-based weapon",
			"damage": 35.0,
			"fire_rate": 5.0,
			"reload_time": 2.0,
			"ammo_per_clip": 50,
			"max_ammo": 200,
			"auto_fire": true,
			"spread": 1.0,
			"weapon_type": "Special",
			"is_dual_wieldable": false,
			"recoil_strength": 0.3,
			"range_max": 1200.0
		}
	]

static func get_template_by_name(template_name: String) -> Dictionary:
	var templates = get_available_templates()
	for template in templates:
		if template.name == template_name:
			return template
	return {}

static func create_custom_template(name: String, base_template: String = "Basic Pistol") -> Dictionary:
	var base = get_template_by_name(base_template)
	if base.is_empty():
		base = get_available_templates()[0]  # Default to first template
	
	var custom = base.duplicate()
	custom.name = name
	custom.description = "Custom weapon based on " + base_template
	return custom
