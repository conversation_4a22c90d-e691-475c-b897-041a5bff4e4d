@tool
extends EditorScript

## Test script for the Weapon Manager Plugin
## Run this script to test weapon creation and export functionality

func _run():
	print("Testing Weapon Manager Plugin...")
	
	# Test weapon resource creation
	test_weapon_resource_creation()
	
	# Test weapon templates
	test_weapon_templates()
	
	# Test weapon scene generation
	test_weapon_scene_generation()
	
	# Test pickup generation
	test_pickup_generation()
	
	print("Weapon Manager Plugin tests completed!")

func test_weapon_resource_creation():
	print("\n=== Testing Weapon Resource Creation ===")
	
	var weapon = WeaponResource.new()
	weapon.weapon_name = "Test Rifle"
	weapon.weapon_description = "A test assault rifle"
	weapon.damage = 30.0
	weapon.fire_rate = 8.0
	weapon.reload_time = 2.0
	weapon.ammo_per_clip = 30
	weapon.max_ammo = 180
	weapon.auto_fire = true
	weapon.weapon_type = "Rifle"
	
	print("Created weapon: ", weapon.weapon_name)
	print("Damage: ", weapon.damage)
	print("Fire rate: ", weapon.fire_rate)
	print("Weapon type: ", weapon.weapon_type)

func test_weapon_templates():
	print("\n=== Testing Weapon Templates ===")
	
	var templates = WeaponTemplates.get_available_templates()
	print("Available templates: ", templates.size())
	
	for template in templates:
		print("- ", template.name, ": ", template.description)

func test_weapon_scene_generation():
	print("\n=== Testing Weapon Scene Generation ===")
	
	var weapon = WeaponResource.new()
	weapon.weapon_name = "Test SMG"
	weapon.damage = 18.0
	weapon.fire_rate = 12.0
	weapon.ammo_per_clip = 25
	weapon.max_ammo = 150
	weapon.weapon_type = "SMG"
	
	var weapon_scene = weapon.generate_weapon_scene()
	if weapon_scene:
		print("Successfully generated weapon scene for: ", weapon.weapon_name)
		
		# Save test scene
		var test_path = "res://test_weapon_scene.tscn"
		var result = ResourceSaver.save(weapon_scene, test_path)
		if result == OK:
			print("Test weapon scene saved to: ", test_path)
		else:
			print("Failed to save test weapon scene")
	else:
		print("Failed to generate weapon scene")

func test_pickup_generation():
	print("\n=== Testing Pickup Generation ===")
	
	var weapon = WeaponResource.new()
	weapon.weapon_name = "Test Shotgun"
	weapon.damage = 80.0
	weapon.fire_rate = 1.2
	weapon.ammo_per_clip = 8
	weapon.max_ammo = 32
	weapon.weapon_type = "Shotgun"
	
	var pickup_scene = WeaponPickupGenerator.generate_pickup_scene(weapon)
	if pickup_scene:
		print("Successfully generated pickup scene for: ", weapon.weapon_name)
		
		# Save test pickup
		var test_path = "res://test_pickup_scene.tscn"
		var result = ResourceSaver.save(pickup_scene, test_path)
		if result == OK:
			print("Test pickup scene saved to: ", test_path)
		else:
			print("Failed to save test pickup scene")
	else:
		print("Failed to generate pickup scene")
