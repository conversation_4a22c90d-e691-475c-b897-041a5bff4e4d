@tool
extends EditorScript

## Test script for Weapon Manager fixes
## Run this script to verify all the improvements work correctly

func _run():
	print("=== Testing Weapon Manager Fixes ===")
	
	# Test weapon persistence
	test_weapon_persistence()
	
	# Test unique naming
	test_unique_naming()
	
	# Test auto-save functionality
	test_auto_save()
	
	print("=== Weapon Manager Tests Completed ===")

func test_weapon_persistence():
	print("\n--- Testing Weapon Persistence ---")
	
	# Check if weapons directory exists
	var weapons_dir = "res://Core/Weapons/Resources/"
	if DirAccess.dir_exists_absolute(weapons_dir):
		print("✓ Weapons directory exists: ", weapons_dir)
		
		# Count existing weapon files
		var dir = DirAccess.open(weapons_dir)
		if dir:
			dir.list_dir_begin()
			var file_name = dir.get_next()
			var weapon_count = 0
			
			while file_name != "":
				if file_name.ends_with(".tres") and file_name.begins_with("weapon_"):
					weapon_count += 1
					print("  Found weapon file: ", file_name)
				file_name = dir.get_next()
			
			print("✓ Found ", weapon_count, " weapon files")
			dir.list_dir_end()
		else:
			print("✗ Could not access weapons directory")
	else:
		print("✗ Weapons directory does not exist")

func test_unique_naming():
	print("\n--- Testing Unique Naming Logic ---")
	
	# Simulate weapon names that already exist
	var existing_names = ["TestWeapon", "TestWeapon_1", "TestWeapon_2", "AnotherWeapon"]
	
	# Test unique name generation
	var test_cases = [
		{"base": "TestWeapon", "expected_pattern": "TestWeapon_3"},
		{"base": "NewWeapon", "expected_pattern": "NewWeapon"},
		{"base": "AnotherWeapon", "expected_pattern": "AnotherWeapon_1"}
	]
	
	for test_case in test_cases:
		var unique_name = _simulate_unique_name_generation(test_case.base, existing_names)
		print("  Base: '", test_case.base, "' -> Unique: '", unique_name, "'")
		
		if unique_name != test_case.base and not unique_name in existing_names:
			print("  ✓ Generated unique name successfully")
		elif unique_name == test_case.base and not test_case.base in existing_names:
			print("  ✓ Base name was already unique")
		else:
			print("  ✗ Failed to generate unique name")

func _simulate_unique_name_generation(base_name: String, existing_names: Array) -> String:
	var unique_name = base_name
	var counter = 1
	
	while unique_name in existing_names:
		unique_name = base_name + "_" + str(counter)
		counter += 1
	
	return unique_name

func test_auto_save():
	print("\n--- Testing Auto-Save Functionality ---")
	
	# Test if ResourceSaver is available
	var test_resource = Resource.new()
	var test_path = "res://test_save.tres"
	
	var save_result = ResourceSaver.save(test_resource, test_path)
	if save_result == OK:
		print("✓ ResourceSaver is working correctly")
		
		# Clean up test file
		if FileAccess.file_exists(test_path):
			DirAccess.open("res://").remove(test_path)
			print("✓ Test file cleaned up")
	else:
		print("✗ ResourceSaver failed with error: ", save_result)

func test_weapon_creation_validation():
	print("\n--- Testing Weapon Creation Validation ---")
	
	# Test name validation logic
	var test_names = [
		{"name": "", "should_be_valid": false},
		{"name": "   ", "should_be_valid": false},
		{"name": "ValidName", "should_be_valid": true},
		{"name": "Another Valid Name", "should_be_valid": true}
	]
	
	for test in test_names:
		var is_valid = test.name.strip_edges() != ""
		var expected = test.should_be_valid
		
		if is_valid == expected:
			print("  ✓ Name '", test.name, "' validation: ", is_valid)
		else:
			print("  ✗ Name '", test.name, "' validation failed. Expected: ", expected, " Got: ", is_valid)

func test_file_operations():
	print("\n--- Testing File Operations ---")
	
	# Test directory creation
	var test_dir = "res://test_weapons/"
	if not DirAccess.dir_exists_absolute(test_dir):
		var result = DirAccess.open("res://").make_dir_recursive(test_dir)
		if result == OK:
			print("✓ Directory creation successful")
		else:
			print("✗ Directory creation failed: ", result)
	else:
		print("✓ Directory already exists")
	
	# Test file deletion
	var test_file = test_dir + "test_weapon.tres"
	var test_resource = Resource.new()
	
	# Create test file
	if ResourceSaver.save(test_resource, test_file) == OK:
		print("✓ Test file created")
		
		# Delete test file
		if DirAccess.open("res://").remove(test_file) == OK:
			print("✓ Test file deleted successfully")
		else:
			print("✗ Test file deletion failed")
	else:
		print("✗ Test file creation failed")
	
	# Clean up test directory
	DirAccess.open("res://").remove(test_dir)

func test_template_system():
	print("\n--- Testing Template System ---")
	
	# Check if WeaponTemplates class is available
	if ClassDB.class_exists("WeaponTemplates"):
		print("✓ WeaponTemplates class found")
	else:
		print("✗ WeaponTemplates class not found")
	
	# Test template loading (if available)
	var templates_script = load("res://addons/weapon_manager/weapon_templates.gd")
	if templates_script:
		print("✓ WeaponTemplates script loaded")
		
		# Try to get templates
		if templates_script.has_method("get_available_templates"):
			print("✓ get_available_templates method found")
		else:
			print("✗ get_available_templates method not found")
	else:
		print("✗ WeaponTemplates script not found")

	print("=== All Tests Completed ===")
	print("Check the output above for any issues that need attention.")
